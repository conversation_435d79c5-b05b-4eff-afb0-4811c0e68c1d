package com.iflytek.rec.anchorpredic.expand;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Collection;

/**
 * 集合元素为null校验
 *
 * <AUTHOR>
 * @since 2024/2/23 10:19
 */
public class CollectionElementValidator implements ConstraintValidator<CollectionElementNotNull, Collection<?>>
{
    @Override
    public void initialize(CollectionElementNotNull constraintAnnotation)
    {

    }

    @Override
    public boolean isValid(Collection<?> value, ConstraintValidatorContext context)
    {
        if (value == null)
        {
            return false;
        }
        for (Object element : value)
        {
            if ((null == element) || ((element instanceof String) && isBlank((String) element)))
            {
                return false;
            }
        }
        return true;
    }

    private boolean isBlank(String str)
    {
        int strLen;
        if (str == null || (strLen = str.length()) == 0)
        {
            return true;
        }
        for (int i = 0; i < strLen; i++)
        {
            if (!(Character.isWhitespace(str.charAt(i))))
            {
                return false;
            }
        }
        return true;
    }
}
