package com.iflytek.rec.anchorpredic.expand;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Check that a collection's element is not null
 *
 * <AUTHOR>
 * @since 2024/2/23 10:19
 */
@Target({ElementType.FIELD, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CollectionElementValidator.class)
public @interface CollectionElementNotNull
{
    String message() default "集合元素不能为null";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
