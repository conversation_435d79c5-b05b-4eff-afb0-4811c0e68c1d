package com.iflytek.rec.anchorpredic.method;

import com.iflytek.rec.anchorpredic.utils.DigitalToChineseUtil;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;
import org.junit.Test;

import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.net.URLEncoder;
import java.net.URLDecoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/9/19 16:46
 */
public class HtmlAnalysis {

    private static final List<String> OPERATOR = Arrays.asList("+", "-", "×", "÷", "=");
    private static final List<String> OPERATOR_CH = Arrays.asList("加", "减", "乘", "除", "等于");

    @Test
    public void urldecodesTest() throws IOException {
//        String URL = "%5E%7B%284%29%5E%20%7B%20%5Ccirc%20%20%7D%7D%5E%20%7B%5Ctextcircled%20%7B000%7D%7D";
        String URL = "%5E%7B%284%29%5E+%7B+%5Ccirc++%7D%7D%5E+%7B%5Ctextcircled+%7B000%7D%7D";
        String Res = URLEncoder.encode("^{(4)^ { \\circ  }}^ {\\textcircled {000}}", "UTF-8");
        System.out.println(Res);
        String Res1 = URLDecoder.decode(URL, "UTF-8");
        System.out.println(Res1);
    }

    @Test
    public static StringBuilder tasktest1(String topicbody) throws IOException {
        /**
         * 深度遍历取出试题题干信息
         * 未解析latex版本
         * */
        Document document = Jsoup.parse(topicbody, "UTF-8");
        Elements html = document.getElementsByTag("html");
        List<Node> p_nodes = new ArrayList<>(html.get(0).childNodes());
        StringBuilder topic_body = new StringBuilder();
        while (p_nodes.size() != 0) {
            Node p_node = p_nodes.get(p_nodes.size() - 1);
            if (p_node.childNodes().size() != 0) {
                p_nodes.remove(p_nodes.size() - 1);
                p_nodes.addAll(p_node.childNodes());
            }
            if (p_node.childNodes().size() == 0) {
                if (p_node.hasAttr("data-latex")) {
                    String Res1 = URLDecoder.decode(p_node.attr("data-latex"), "UTF-8");
                    topic_body.insert(0, Res1);
                    p_nodes.remove(p_nodes.size() - 1);
                } else {
                    if (p_node.attributes().size() == 1 && !p_node.attributes().hasKey("src")) {
                        topic_body.insert(0, p_node);
                        p_nodes.remove(p_nodes.size() - 1);
                    } else {
                        p_nodes.remove(p_nodes.size() - 1);
                    }
                }
            }

        }
        return topic_body;
    }

//    @Test
//    public static StringBuilder tasktest2(String topicbody) throws IOException {
//        /**
//         * 深度遍历取出试题题干信息
//         * 解析latex版本
//         * */
//        Document document = Jsoup.parse(topicbody, "UTF-8");
//        Elements html = document.getElementsByTag("html");
//        List<Node> p_nodes = new ArrayList<>(html.get(0).childNodes());
//        StringBuilder topic_body = new StringBuilder();
//        while (p_nodes.size() != 0) {
//            Node p_node = p_nodes.get(p_nodes.size() - 1);
//            if (p_node.childNodes().size() != 0) {
//                p_nodes.remove(p_nodes.size() - 1);
//                p_nodes.addAll(p_node.childNodes());
//            }
//            if (p_node.childNodes().size() == 0) {
//                if (p_node.hasAttr("data-latex")) {
//                    String res1 = URLDecoder.decode(p_node.attr("data-latex"), "UTF-8");
//                    String res2 = Latex2Mathml.latexToMathml(res1);
////                    System.out.println("latex: " + res1 + "\n" + "mathMl：" + res2 + "\n");
//                    topic_body.insert(0, mathMlToStr(res2));
//                    p_nodes.remove(p_nodes.size() - 1);
//                } else {
//                    if (p_node.attributes().size() == 1 && !p_node.attributes().hasKey("src")) {
//                        topic_body.insert(0, p_node);
//                        p_nodes.remove(p_nodes.size() - 1);
//                    } else {
//                        p_nodes.remove(p_nodes.size() - 1);
//                    }
//                }
//            }
//
//        }
//        return topic_body;
//    }

    @Test
    public static StringBuilder mathMlToStr(String topicbody) throws IOException {
        /**
         * 深度遍历取出mathMl公式信息
         * 解析mathMl
         * */
        Document document = Jsoup.parse(topicbody, "UTF-8");
        Elements html = document.getElementsByTag("math");
        List<Node> p_nodes = new ArrayList<>(html.get(0).childNodes());
        StringBuilder topic_body = new StringBuilder();
        while (p_nodes.size() != 0) {
            Node p_node = p_nodes.get(p_nodes.size() - 1);
            if (p_node.childNodes().size() != 0) {
                p_nodes.remove(p_nodes.size() - 1);
                p_nodes.addAll(p_node.childNodes());
            }
            if (p_node.childNodes().size() == 0) {
                topic_body.insert(0, p_node);
                p_nodes.remove(p_nodes.size() - 1);
            }

        }
//        System.out.println("深度遍历取出mathMl公式信息:" + topic_body + "\n");
        return topic_body;
//        System.out.println("--------------------------------------------------------");

    }

    /**
     * 1、去除题号和得分  保留中间题干
     * 2、整数和小数转为汉字
     * 3、latex公式不解析转化
     */
    @Test
    public void solution1() throws IOException {
        String input = "C:\\Users\\<USER>\\Desktop\\java基础\\spring-boot-test01\\xmlTool\\src\\main\\resources\\topic_test.html";
        String contentLine = null;
        //去除题号和得分  保留中间题干
        String pattern = "\\d{1}\\.|\\(\\d+\\)|\\(.*\\d+.*分.*\\)";
        //整数和小数转为汉字
        String pattern1 = "-\\d+\\.[1-9]+|\\d+\\.[1-9]+|-\\d+|\\d+";
        Pattern p = Pattern.compile(pattern);
        Pattern p1 = Pattern.compile(pattern1);
        InputStream is = Files.newInputStream(Paths.get(input));
        BufferedReader reader = new BufferedReader(new InputStreamReader(is));
        int i = 0;
        while ((contentLine = reader.readLine()) != null) {
            System.out.println("入参：" + contentLine + "\n");
            StringBuilder topic_body = tasktest1(contentLine);
//            System.out.println(topic_body + "\n");
            //去除题号和得分  保留中间题干
            Matcher m = p.matcher(topic_body);
            StringBuffer sb = new StringBuffer();
            while (m.find()) {
                if (m.start() == 0) {
                    m.appendReplacement(sb, "");
                }
                if (m.group(0).contains("分")) {
                    m.appendReplacement(sb, "");
                }
            }
            m.appendTail(sb);
//            System.out.println("出参sb：" + sb + "\n");

            //整数和小数转为汉字
            Matcher m1 = p1.matcher(sb);
            StringBuffer sb1 = new StringBuffer();
            while (m1.find()) {
//                System.out.println(m1.group() + ":" + DigitalToChineseUtil.bigDecimal2chineseNum(BigDecimal.valueOf(Long.parseLong(m1.group()))));
                m1.appendReplacement(sb1, DigitalToChineseUtil.bigDecimal2chineseNum(BigDecimal.valueOf(Double.parseDouble(m1.group()))));
            }
            m1.appendTail(sb1);
            System.out.println("出参：" + sb1 + "\n");
            i++;
            System.out.println(i + "==================================================");

        }
        is.close();

    }

    /**
     * 1、去除题号和得分  保留中间题干
     * 2、整数和小数转为汉字
     * 3、latex公式解析转化
     */
//    @Test
//    public void solution2() throws IOException {
//        String input = "C:\\Users\\<USER>\\Desktop\\java基础\\spring-boot-test01\\xmlTool\\src\\main\\resources\\topic_test.html";
//        String contentLine = null;
//        //去除题号和得分  保留中间题干
//        String pattern = "\\d{1}\\.|\\(\\d+\\)|\\(.*\\d+.*分.*\\)";
//        //整数和小数转为汉字
//        String pattern1 = "-\\d+\\.[1-9]+|\\d+\\.[1-9]+|-\\d+|\\d+";
//        //+-×÷=映射为汉字：加减乘除等于
//        String pattern2 = "[\\+\\-\\×\\÷\\=]{1}";
//        Pattern p = Pattern.compile(pattern);
//        Pattern p1 = Pattern.compile(pattern1);
//        Pattern p2 = Pattern.compile(pattern2);
//        InputStream is = Files.newInputStream(Paths.get(input));
//        BufferedReader reader = new BufferedReader(new InputStreamReader(is));
//        int i = 0;
//        while ((contentLine = reader.readLine()) != null) {
//            System.out.println("入参：" + contentLine + "\n");
//            StringBuilder topic_body = tasktest2(contentLine);
////            System.out.println(topic_body + "\n");
//            //去除题号和得分  保留中间题干
//            Matcher m = p.matcher(topic_body);
//            StringBuffer sb = new StringBuffer();
//            while (m.find()) {
//                if (m.start() == 0) {
//                    m.appendReplacement(sb, "");
//                }
//                if (m.group(0).contains("分")) {
//                    m.appendReplacement(sb, "");
//                }
//            }
//            m.appendTail(sb);
////            System.out.println("出参sb：" + sb + "\n");
//
//            //整数和小数转为汉字
//            Matcher m1 = p1.matcher(sb);
//            StringBuffer sb1 = new StringBuffer();
//            while (m1.find()) {
////                System.out.println(m1.group() + ":" + DigitalToChineseUtil.bigDecimal2chineseNum(BigDecimal.valueOf(Long.parseLong(m1.group()))));
//                m1.appendReplacement(sb1, DigitalToChineseUtil.bigDecimal2chineseNum(BigDecimal.valueOf(Double.parseDouble(m1.group()))));
//            }
//            m1.appendTail(sb1);
//
////            String sb2 =  sb1.toString().replace("\n","");
//            //+-×÷=映射为汉字：加减乘除等于
//            Matcher m2 = p2.matcher(sb1);
//            StringBuffer sb2 = new StringBuffer();
//            while (m2.find()) {
//                if (OPERATOR.contains(m2.group())) {
//                    m2.appendReplacement(sb2, OPERATOR_CH.get(OPERATOR.indexOf(m2.group())));
//                }
//            }
//            m2.appendTail(sb2);
//            System.out.println("出参：" + sb2.toString().replace("\n", "") + "\n");
//            i++;
//            System.out.println(i + "==================================================");
//
//        }
//        is.close();
//
//    }

    @Test
    public void tasktest() throws IOException {
        File input = new File("src/main/resources/topic_test1.html");
        Document document = Jsoup.parse(input, "UTF-8");
        Elements divs = document.getElementsByTag("div");
        for (Element div : divs) {
            System.out.println("---------------------------------start--------------------------------------");
            System.out.println(div);
            System.out.println("---------------------------------middle--------------------------------------");
            Elements ps = div.getElementsByTag("p");
            StringBuilder topic_body = new StringBuilder();
            for (Element p : ps) {
                System.out.println(p);
                System.out.println(p.attr("class"));
                System.out.println(p.text());
                List<Node> p_nodes = p.childNodes();
                System.out.println("-----------------------------------p_node------------------------------------");
                for (Node p_node : p_nodes) {
                    if (p_node.hasAttr("data-latex")) {
                        String Res1 = URLDecoder.decode(p_node.attr("data-latex"), "UTF-8");
                        System.out.println(Res1);
                        topic_body.append(Res1);
                    } else {
                        if (p_node.attributes().size() == 1 && !p_node.attributes().hasKey("src")) {
                            System.out.println(p_node.toString());
                            topic_body.append(p_node);
                        }
                    }
                }
            }
            System.out.println(topic_body);
            System.out.println("-----------------------------------end------------------------------------");
//            break;
        }
    }

//    @Test
//    public void latex2mathmlTest() throws IOException {
//        String latex = "x = {-b \\pm \\sqrt{b^2-4ac} \\over 2a}";
//        System.out.println(Latex2Mathml.latexToMathml(latex));
//
//    }

    @Test
    public void regex1Test() {
        // 按指定模式在字符串查找
//        String line = "1.写出下面物体的个数。( )辆( )把( )面";
        String line = "215里(3)面3.677..893,-986.67,98.754,6.7,8.90有几(5分)个3?(4分)";
//        String pattern = "\\d{1}\\.|\\(\\d+\\)";
//        String pattern = "\\d{1}\\.|\\(\\d+\\)|\\(\\d+分\\)";
//        String pattern = "\\d{1}\\.\\D]|\\(\\d+\\)|\\(.*\\d+.*分.*\\)|-\\d+\\.\\d+|\\d+\\.\\d+|-\\d+|\\d+";
        String pattern = "-\\d+\\.[1-9]+|\\d+\\.[1-9]+|-\\d+|\\d+";
        String pattern1 = "\\(\\d+分\\)";

        // 创建 Pattern 对象
        Pattern r = Pattern.compile(pattern);
        Pattern r1 = Pattern.compile(pattern1);

        // 现在创建 matcher 对象
        Matcher m = r.matcher(line);
        String res = "";
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            System.out.println("Found value: " + m.groupCount());
            System.out.println("mstart: " + m.start());
            System.out.println("mend: " + m.end());
            System.out.println("Found value: " + m.group(0));
            System.out.println("----------");
            if (m.start() == 0) {
                m.appendReplacement(sb, "");
            }
            if (m.group(0).contains("分")) {
                m.appendReplacement(sb, "");
            }
        }
        m.appendTail(sb);
        System.out.println(sb);
    }

    @Test
    public void regexTest() {
//        String REGEX = "a*cb|bk";
        String REGEX = "(\\D*)(\\d+)(.*)";
        String INPUT = " acb  foobkkk";
        String REPLACE = "-";
        Pattern p = Pattern.compile(REGEX);
        // 获取 matcher 对象
        Matcher m = p.matcher(INPUT);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            System.out.println(m.groupCount());
            System.out.println(m.group(0));
            System.out.println(m.group(1));
            System.out.println(m.group(2));
            System.out.println(m.group(3));
            m.appendReplacement(sb, REPLACE);
        }
        m.appendTail(sb);
//        System.out.println(sb.toString());
    }

    /**
     * 功能：解析html文件，得到指定数据
     */
    @Test
    public void analysisTest() throws IOException {
        File input = new File("src/main/resources/testhtml.html");
        Document document = Jsoup.parse(input, "UTF-8");
        //根据class得到列表元素
        Element first = document.getElementsByClass("js-awemelist").first();
        //根据class得到列表下的所有数据
        Elements elementsByClass = first.getElementsByClass("js-slider-aweme");

        List<String> jsonList = new ArrayList<>();
        //遍历以上列表
        for (Element byClass : elementsByClass) {

            String json = "";

            System.out.println("id:" + byClass.getElementsByClass("js-open-aweme-pop").attr("data-id"));
            json += "id:" + byClass.getElementsByClass("js-open-aweme-pop").attr("data-id") + ",";

            System.out.println("awemeid:" + byClass.getElementsByClass("js-open-aweme-pop").attr("data-awemeid"));
            json += "awemeid:" + byClass.getElementsByClass("js-open-aweme-pop").attr("data-awemeid") + ",";

            //得到视频名称
            /*System.out.println("视频名称："+byClass.getElementsByClass("item-title").first().text());
            json += "视频名称:"+byClass.getElementsByClass("item-title").first().text()+",";*/

            //发布作者
//            System.out.println("作者名称:"+byClass.getElementsByClass("item-title").text().replaceAll(byClass.getElementsByClass("item-title").first().text()+" ",""));
//            json += "作者名称:"+byClass.getElementsByClass("item-title").text().replaceAll(byClass.getElementsByClass("item-title").first().text()+" ","") +",";
            int count = 0;
            for (Element aClass : byClass.getElementsByClass("item-title")) {
                if (count == 0) {
                    //得到视频名称
                    System.out.println("视频名称:" + aClass.getElementsByTag("a").text());
                    json += "视频名称:" + aClass.getElementsByTag("a").text() + ",";
                }

                if (count == 1) {
                    //发布作者
                    System.out.println("作者名称:" + aClass.getElementsByTag("a").text());
                    json += "作者名称:" + aClass.getElementsByTag("a").text() + ",";
                }
                count++;
            }

            //视频时长
            System.out.println("视频时长：" + byClass.getElementsByClass("item-times").text().replaceAll("视频时长：", ""));
            json += "视频时长:" + byClass.getElementsByClass("item-times").text().replaceAll("视频时长: ", "") + ",";

            //得到视频头图
            System.out.println("头图：" + byClass.getElementsByTag("img").attr("src"));
            json += "头图:" + byClass.getElementsByTag("img").attr("src") + ",";

            //视频地址
            System.out.println("视频地址：" + byClass.getElementsByClass("source-play").attr("href"));
            json += "视频地址:" + byClass.getElementsByClass("source-play").attr("href") + ",";

            //热词解析
            Elements ul = byClass.getElementsByTag("ul");
            for (Element element : ul) {
                //得到视频热词
                System.out.println("视频热词：" + element.getElementsByTag("a").text());
                json += "视频热词:" + element.getElementsByTag("a").text() + ",";
            }

//            Elements elementsByClass1 = byClass.getElementsByClass("item-title");

            String substring = json.substring(0, json.length() - 1);

            jsonList.add(substring);

            System.out.println("---------------------------------------------------------------------------------------------------------");

        }

        for (String s : jsonList) {
            System.out.println(s);
        }

    }
}
