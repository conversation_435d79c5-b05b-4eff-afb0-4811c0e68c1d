package com.iflytek.rec.anchorpredic.method.regexp;

import com.iflytek.rec.anchorpredic.impl.AnchorPredicMethodImpl;
import com.iflytek.rec.anchorpredic.method.Fraction;
import com.iflytek.rec.anchorpredic.utils.DigitalToChineseUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/12/7 13:46
 */
public class CommenRegExp {

    private static final Logger log = LoggerFactory.getLogger(CommenRegExp.class);
    static ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
    //todo 解除警告
    public static final ScriptEngine scriptEngine = scriptEngineManager.getEngineByName("nashorn");
    static final Pattern patternEq = Pattern.compile("等于");


//    public static Pattern pBody;
//    public static Pattern pBodyPrimary;


    public static StringBuffer regexp(String content) {
        //1.去除不可见字符（\u200b,\u200c）
        content = content.replaceAll("\\u200b|\\u200c", "");
        content = content.replaceAll("^\\d+\\.", "");
        //4-8
        content = content.replaceAll("(^(, )*\\(\\d+\\)\\s*[。、])|(^(, )*\\w+\\s*[。、])", "");
        content = content.replaceAll("^[一二三四五六七八九十]+\\s*[.。、]", "");
        content = content.replaceAll(" ", "");
        content = content.replaceAll("^\\\\textcircled\\s*\\{\\d+\\}", "");
        //删除分数
//        content = content.replaceAll("\\(\\s*\\d+\\s*分\\s*\\)|\\（\\s*\\d+\\s*分\\s*\\）", "");
        content = content.replaceAll("\\(\\d*\\)|\\（\\d*\\）|[A-Z]\\.", " ");
        content = content.replaceAll("——————|[≈＝]", "=");

        //9.分数提取
        Pattern pBody = Pattern.compile("\\\\frac\\s*\\{(.*?)\\}\\s*\\{(.*?)\\}");
        Matcher m = pBody.matcher(content);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            m.appendReplacement(sb, grade(m.group(1), m.group(2)));
        }
        m.appendTail(sb);
        content = sb.toString();

        //10.数学公式
        pBody = Pattern.compile("([0-9+×÷.-]{3,})=([0-9+×÷()-]*)");
        m = pBody.matcher(content);
        sb = new StringBuffer();
        while (m.find()) {
            try {
                String result = m.group(0);
                if (m.group(2).isEmpty() && !isDouble(m.group(1))) {
                    String calcu = m.group(1).replaceAll("×", "*");
                    calcu = calcu.replaceAll("÷", "/");
                    String res = "";
                    if (calcu.matches("^[0-9].*[-+*/].*")) {
                        //1+2+3+4++2022
                        if (!isNotEval(calcu)) {
                            res = "" + scriptEngine.eval(calcu);
                        }
                    }
                    if (isDouble(res)) {
                        //保留4位小数
                        res = String.format("%.4f", Double.parseDouble(res));
                    }
                    result = m.group(1) + "=" + res;
                    result = calculate(result);
                }
                m.appendReplacement(sb, result);

            } catch (ScriptException e) {
//                e.printStackTrace();
                log.error(String.valueOf(e));
            }
        }
        m.appendTail(sb);
        content = sb.toString();

        //选项数字转汉字
        //小数转换
        pBody = Pattern.compile("(\\d+)\\s*\\.\\s*(\\d+)");
        m = pBody.matcher(content);
        sb = new StringBuffer();
        while (m.find()) {
            if (m.group(1).length() < 12) {
                m.appendReplacement(sb, DigitalToChineseUtil.bigDecimal2chineseNum(BigDecimal.valueOf(Double.parseDouble(m.group(0).replaceAll(" ", "")))));
            }
        }
        m.appendTail(sb);
        content = sb.toString();
        //立方根转为UNK  若√{a+8}与(b-27)^{2}互为相反数,求√[3]{a}-√[3]{b}的立方根.
        content = content.replaceAll("√\\[3\\]\\{[a-z]+\\}", "∘");
        content = content.replaceAll("√\\[3\\]", "∘");
        //整数转换
        pBody = Pattern.compile("(\\d+)");
        m = pBody.matcher(content);
        sb = new StringBuffer();
        while (m.find()) {
            if (m.group(1).length() < 12) {
                m.appendReplacement(sb, DigitalToChineseUtil.bigDecimal2chineseNum(BigDecimal.valueOf(Long.parseLong(m.group(0)))));
            }
        }
        m.appendTail(sb);
        content = sb.toString();

        pBody = Pattern.compile("\\\\[a-z]+\\{\\s*(.*?)\\s*\\}");
        m = pBody.matcher(content);
        sb = new StringBuffer();
        while (m.find()) {
            m.appendReplacement(sb, m.group(1));
        }
        m.appendTail(sb);
        content = sb.toString();

        //left左括号a加bright右括号
        content = content.replaceAll("\\\\left|\\\\right", "");
        content = content.replaceAll("\\(\\s*\\)|\\（\\s*\\）|\\（　　*\\）|\\(　　*\\)", "");
        content = content.replaceAll("\\\\", "");
        content = content.replaceAll("\\(|（", "左括号");
        content = content.replaceAll("\\)|）", "右括号");
        content = content.replaceAll("/", "比");
        return new StringBuffer(content);
    }


    public static StringBuffer regexpPrimary(String content) {
        //1.去除不可见字符（\u200b,\u200c）
        content = content.replaceAll("\\u200b|\\u200c", "");
        content = content.replaceAll("^\\d+\\.", "");
        //4-8
        content = content.replaceAll("(^(, )*\\(\\d+\\)\\s*[。、])|(^(, )*\\w+\\s*[。、])", "");
        content = content.replaceAll("^[一二三四五六七八九十]+\\s*[.。、]", "");
        content = content.replaceAll(" ", "");
        content = content.replaceAll("^\\\\textcircled\\s*\\{\\d+\\}", "");
        //删除分数
        content = content.replaceAll("\\([共]?\\s*\\d+\\s*分\\s*\\)|\\（[共]?\\s*\\d+\\s*分\\s*\\）", "");
        content = content.replaceAll("\\(\\d+\\)|\\（\\d+\\）|[A-Z]\\.", "\t");
        content = content.replaceAll("——————|[≈＝]", "=");

        //9.分数提取
        Pattern pBodyPrimary = Pattern.compile("\\\\frac\\s*\\{(.*?)\\}\\s*\\{(.*?)\\}");
        Matcher m = pBodyPrimary.matcher(content);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            m.appendReplacement(sb, gradePriamary(m.group(1), m.group(2)));
        }
        m.appendTail(sb);
        content = sb.toString();
        //todo 1
        content = content.replaceAll("\\(\\)|\\（\\）", "");
        pBodyPrimary = Pattern.compile("\\\\begin\\{[a-zA-Z]*\\}(.*?)\\\\end\\{[a-zA-Z]*\\}");
        m = pBodyPrimary.matcher(content);
        sb = new StringBuffer();
        while (m.find()) {
            m.appendReplacement(sb, m.group(1));
        }
        m.appendTail(sb);
        content = sb.toString();


        //10.数学公式
        pBodyPrimary = Pattern.compile("([0-9+/*×÷.()-]{3,})=([0-9+/*×÷()-]*)");
        m = pBodyPrimary.matcher(content);
        sb = new StringBuffer();
        while (m.find()) {
            try {
                String result = m.group(0);
                if (m.group(2).isEmpty() && !isDouble(m.group(1))) {
                    String calcu = m.group(1).replaceAll("×", "*");
                    calcu = calcu.replaceAll("÷", "/");
                    String res = "";
                    if (calcu.matches("^[0-9].*[-()+*/].*")) {
                        if (!isNotEval(calcu)) {
                            res = calcuRes(m.group(1), calcu);
                        }
                    }
                    if (calcu.matches("[0-9]+")) {
                        res = calcu;
                    }
                    if (isDouble(res)) {
                        //保留4位小数
                        String[] split = res.split("\\.");
                        if (split[1].length() > 4) {
                            res = String.format("%.4f", Double.parseDouble(res));
                            res = res.replaceAll("0+$", "0");
                            res = res.replaceAll("(\\.[1-9]+)0$", "$1");
                        }
                    }
                    result = m.group(1) + "=" + res;
                    result = calculatePrimary(result);
                }
                //todo 2
                else if (!isDouble(m.group(1)) && !m.group(2).matches(".*[0-9].*")) {
                    String calcu = m.group(1).replaceAll("×", "*");
                    calcu = calcu.replaceAll("÷", "/");
                    String res = "";
                    if (calcu.matches("^[0-9].*[-()+*/].*")) {
                        if (!isNotEval(calcu)) {
                            res = calcuRes(m.group(1), calcu);
                        }
                        result = m.group(0) + "=" + res;
                        result = calculatePrimary(result);
                    }
                }
                m.appendReplacement(sb, result);

            } catch (Exception e) {
//                e.printStackTrace();
                log.error(String.valueOf(e));
            }
        }
        m.appendTail(sb);
        content = sb.toString();

        content = content.replaceAll(" ", "");
        //分数转换
        pBodyPrimary = Pattern.compile("([0-9]+\\.?[0-9]*)/([0-9]+\\.?[0-9]*)");
        m = pBodyPrimary.matcher(content);
        sb = new StringBuffer();
        while (m.find()) {
            String one = doubleOrLong(m.group(1)).replaceAll("&.*数", "");
            String two = doubleOrLong(m.group(2)).replaceAll("&.*数", "");
            String res = one + "比" + two + "&分数";
            m.appendReplacement(sb, res);
        }
        m.appendTail(sb);
        content = sb.toString();

        //形如 xxx.xxx.xx 只有第一个点被视为有效的点
        pBodyPrimary = Pattern.compile("(\\d+\\.\\d+)(\\.\\d+)+");
        m = pBodyPrimary.matcher(content);
        sb = new StringBuffer();
        while (m.find()) {
            String res = m.group(1) + m.group(2).replaceAll("\\.", "");
            m.appendReplacement(sb, res);
        }
        m.appendTail(sb);
        content = sb.toString();

        //选项数字转汉字
        //小数转换
        pBodyPrimary = Pattern.compile("(\\d+)\\s*\\.\\s*(\\d+)");
        m = pBodyPrimary.matcher(content);
        sb = new StringBuffer();
        while (m.find()) {
            if (m.group(1).length() < 12) {
                m.appendReplacement(sb, DigitalToChineseUtil.bigDecimal2chineseNumPrimary(new BigDecimal(m.group(0).replaceAll(" |\\t", ""))));
            }
        }
        m.appendTail(sb);
        content = sb.toString();
        //立方根转为UNK  若√{a+8}与(b-27)^{2}互为相反数,求√[3]{a}-√[3]{b}的立方根.
        content = content.replaceAll("√\\[3\\]\\{[a-z]+\\}", "∘");
        content = content.replaceAll("√\\[3\\]", "∘");
        //todo 3 比 分数变换
        pBodyPrimary = Pattern.compile("/(\\d+)");
        m = pBodyPrimary.matcher(content);
        sb = new StringBuffer();
        while (m.find()) {
            if (m.group(1).length() < 12) {
                String res = "/" + DigitalToChineseUtil.bigDecimal2chineseNumPrimary(BigDecimal.valueOf(Long.parseLong(m.group(1)))).replaceAll("&.*位数", "&分数");
                m.appendReplacement(sb, res);
            }
        }
        m.appendTail(sb);
        content = sb.toString();
        //整数转换
        pBodyPrimary = Pattern.compile("(\\d+)");
        m = pBodyPrimary.matcher(content);
        sb = new StringBuffer();
        while (m.find()) {
            if (m.group(1).length() < 12) {
                m.appendReplacement(sb, DigitalToChineseUtil.bigDecimal2chineseNumPrimary(BigDecimal.valueOf(Long.parseLong(m.group(0)))));
            }
        }
        m.appendTail(sb);
        content = sb.toString();

        pBodyPrimary = Pattern.compile("\\\\[a-z]+\\{\\s*(.*?)\\s*\\}");
        m = pBodyPrimary.matcher(content);
        sb = new StringBuffer();
        while (m.find()) {
            m.appendReplacement(sb, m.group(1));
        }
        m.appendTail(sb);
        content = sb.toString();

        //left左括号a加bright右括号
        content = content.replaceAll("\\\\left|\\\\right", "");
        content = content.replaceAll("\\(\\s*\\)|\\（\\s*\\）|\\（　　*\\）|\\(　　*\\)", "");
        content = content.replaceAll("\\\\", "");
        content = content.replaceAll("\\(|（", "左括号");
        content = content.replaceAll("\\)|）", "右括号");
        content = content.replaceAll("/", "比");
        content = content.replaceAll("⋯|······", "余");
        content = content.replaceAll("\\t", "");

        return new StringBuffer(content);
    }

    public static String regexpPhysics(String content) {
        // 算式(组)解析
        content = content.replaceAll("\\\\begin\\{.*?\\}(.*?)\\\\end\\{.*?\\}", "^$1");
        // 题号删除
        content = content.replaceAll("^\\(\\d\\)[\\.|。]", "");
        content = content.replaceAll("^\\d+[\\.|。]", "");
        content = content.replaceAll("^[0-9一二三四五六七八九十]+、", "");
        content = content.replaceAll("\\\\textcircled\\{\\d+\\}", "");
        content = content.replaceAll("\\\\\\\\textcircled\\{\\d+\\}", "");
        // 选项删除
        content = content.replaceAll("[A-Z]\\.", ";");
        content = content.replaceAll("（\\d+）", ";");
        content = content.replaceAll("\\(\\d+\\)", ";");
        // 答案填写处删除
        content = content.replaceAll("\\(\\s*\\)", "");
        content = content.replaceAll("（\\s*）", "");
        content = content.replaceAll("_+", "");
        // 得分删除
        content = content.replaceAll("\\(共*\\d+分\\)", "");
        // 计算式处理
        content = content.replaceAll("\\\\frac\\s*\\{(.*?)\\}\\s*\\{(.*?)\\}", " $1/$2 ");

        // 数学公式
        //整数和小数转为汉字
        StringBuffer sb1 = AnchorPredicMethodImpl.patternNumToWorldPhysics(content);
        //+-×÷=映射为汉字：加减乘除等于
        String sb2 = AnchorPredicMethodImpl.patternOperatorToWorldPhysics(sb1);
        content = sb2.toString();

        // 其他格式符处理
        content = content.replaceAll("\\\\[a-z]+\\s*\\{\\s*(.*?)\\s*\\}", "$1");
//        content = content.replaceAll("\\\\left|\\\\right|\\\\rm", "");
        content = content.replaceAll("\\\\left|\\\\right|\\\\rm|\\\\matrix|\\\\beginaligned|\\\\endaligned|\\\\unk|\\\\ding|\\\\begin|\\\\", "");
        Matcher m = patternEq.matcher(content);
        int i = 0;
        while (m.find()) {
            i++;
        }
        if (i > 1) {
            content = content.replaceAll("等于", "等于;");
        }
        content = content.replaceAll("\\s+", "");

        return content;
    }
    public static String regexpHighMath(String content) {
        // 算式(组)解析
        content = content.replaceAll("\\\\begin\\{.*?\\}(.*?)\\\\end\\{.*?\\}", "^$1");
        // 题号删除
        content = content.replaceAll("^\\(\\d\\)[\\.|。]", "");
        content = content.replaceAll("^\\d+[\\.|。]", "");
        content = content.replaceAll("^[0-9一二三四五六七八九十]+、", "");
        content = content.replaceAll("\\\\textcircled\\{\\d+\\}", "");
        content = content.replaceAll("\\\\\\\\textcircled\\{\\d+\\}", "");
        // 选项删除
        content = content.replaceAll("[A-Z]\\.", ";");
        content = content.replaceAll("（\\d+）", ";");
        content = content.replaceAll("\\(\\d+\\)", ";");
        // 答案填写处删除
        content = content.replaceAll("\\(\\s*\\)", "");
        content = content.replaceAll("（\\s*）", "");
        content = content.replaceAll("_+", "");
        // 得分删除
        content = content.replaceAll("\\(共*\\d+分\\)", "");
        // 计算式处理
        content = content.replaceAll("\\\\frac\\s*\\{(.*?)\\}\\s*\\{(.*?)\\}", " $1/$2 ");

        // 数学公式
        //整数和小数转为汉字
//        StringBuffer sb1 = AnchorPredicMethodImpl.patternNumToWorldPhysics(content);
        //+-×÷=映射为汉字：加减乘除等于
//        String sb2 = AnchorPredicMethodImpl.patternOperatorToWorldPhysics(sb1);
//        content = sb2.toString();

        // 其他格式符处理
        content = content.replaceAll("\\\\[a-z]+\\s*\\{\\s*(.*?)\\s*\\}", "$1");
//        content = content.replaceAll("\\\\left|\\\\right|\\\\rm", "");
        content = content.replaceAll("\\\\left|\\\\right|\\\\rm|\\\\matrix|\\\\beginaligned|\\\\endaligned|\\\\unk|\\\\ding|\\\\begin|\\\\", "");
        Matcher m = patternEq.matcher(content);
        int i = 0;
        while (m.find()) {
            i++;
        }
        if (i > 1) {
            content = content.replaceAll("等于", "等于;");
        }
        content = content.replaceAll("\\s+", "");
        content = content.replaceAll("\\{(\\S)\\}", "$1");
        content = content.replaceAll("\\{(\\w+)\\}", "$1");

        return content;
    }

    public static String calcuRes(String calcuOri, String calcu) {
        String res = "";
        try {
            if (hasDecimal(calcuOri)) {
                while (hasFraction(calcu)) {
                    StringBuffer sb = new StringBuffer();
                    Pattern pattern = Pattern.compile("([0-9]+\\.?[0-9]*)/([0-9]*+\\.?[0-9]*)");
                    Matcher m = pattern.matcher(calcu);
                    while (m.find()) {
                        String re = String.valueOf(bigDecimalDiv(m.group(1), m.group(2)));
                        m.appendReplacement(sb, re);
                    }
                    m.appendTail(sb);
                    calcu = sb.toString();
                }
                while (calcu.matches(".*\\(.*\\).*")) {
                    StringBuffer sb = new StringBuffer();
                    Pattern pattern = Pattern.compile("\\((.*?)\\)");
                    Matcher m = pattern.matcher(calcu);
                    while (m.find()) {
                        String re = calcuDouble(m.group(1));
                        m.appendReplacement(sb, re);
                    }
                    m.appendTail(sb);
                    calcu = sb.toString();
                }
                return calcuDouble(calcu);
            } else if (hasFraction(calcuOri)) {
                while (calcuOri.matches(".*\\(.*\\).*")) {
                    StringBuffer sb = new StringBuffer();
                    Pattern pattern = Pattern.compile("\\((.*?)\\)");
                    Matcher m = pattern.matcher(calcuOri);
                    while (m.find()) {
                        String re = calcuFraction(m.group(1));
                        m.appendReplacement(sb, re);
                    }
                    m.appendTail(sb);
                    calcuOri = sb.toString();
                }
                return calcuFraction(calcuOri);
            } else {
                Long zhengs = Math.round(Double.parseDouble("" + scriptEngine.eval(calcu)));
                return String.valueOf(zhengs);
            }
        } catch (Exception e) {
//            e.printStackTrace();
            log.error(String.valueOf(e));
            return res;
        }
    }

    public static BigDecimal bigDecimalDiv(String one, String two) {
        BigDecimal bigDecimal;
        try {
            String ca = one + "/" + two;
            String res = "" + scriptEngine.eval(ca);
            bigDecimal = new BigDecimal(res);
//            bigDecimal = new BigDecimal(one).divide(new BigDecimal(two));
        } catch (Exception e) {
            bigDecimal = new BigDecimal(one).divide(new BigDecimal(two), 4, RoundingMode.DOWN);
        }
        return bigDecimal;
    }

    public static String calcuFraction(String calcuOri) {

        try {
            while (calcuOri.matches(".*[0-9][×*÷][0-9].*")) {
                StringBuffer sb = new StringBuffer();
                Pattern pattern = Pattern.compile("([^+*×÷-]+)([×*÷])([^+*×÷-]+)");
                Matcher m = pattern.matcher(calcuOri);
                while (m.find()) {
                    Fraction n1 = fraction(m.group(1));
                    Fraction n2 = fraction(m.group(3));
                    String res = "";
                    if (StringUtils.equals(m.group(2), "×")) {
                        res = String.valueOf(n1.multiply(n2));
                    }
                    if (StringUtils.equals(m.group(2), "*")) {
                        res = String.valueOf(n1.multiply(n2));
                    }
                    if (StringUtils.equals(m.group(2), "÷")) {
                        res = String.valueOf(n1.divide(n2));
                    }
                    m.appendReplacement(sb, res);
                }
                m.appendTail(sb);
                calcuOri = sb.toString();
            }
            while (calcuOri.matches(".*[0-9]-[0-9].*")) {
                StringBuffer sb = new StringBuffer();
                Pattern pattern = Pattern.compile("([^+*×÷-]+)([-])([^+*×÷-]+)");
                Matcher m = pattern.matcher(calcuOri);
                while (m.find()) {
                    Fraction n1 = fraction(m.group(1));
                    Fraction n2 = fraction(m.group(3));
                    String res = "";
                    if (StringUtils.equals(m.group(2), "-")) {
                        res = String.valueOf(n1.sub(n2));
                    }
                    m.appendReplacement(sb, res);
                }
                m.appendTail(sb);
                calcuOri = sb.toString();
                calcuOri = calcuOri.replaceAll("\\+-|--|-+", "-");
            }
            while (calcuOri.matches(".*[0-9][+-][0-9].*")) {
                StringBuffer sb = new StringBuffer();
                Pattern pattern = Pattern.compile("([^+*×÷-]+)([+-])([^+*×÷-]+)");
                Matcher m = pattern.matcher(calcuOri);
                while (m.find()) {
                    Fraction n1 = fraction(m.group(1));
                    Fraction n2 = fraction(m.group(3));
                    String res = "";
                    if (StringUtils.equals(m.group(2), "+")) {
                        res = String.valueOf(n1.add(n2));
                    }
                    if (StringUtils.equals(m.group(2), "-")) {
                        res = String.valueOf(n1.sub(n2));
                    }
                    m.appendReplacement(sb, res);
                }
                m.appendTail(sb);
                calcuOri = sb.toString();
            }
            return calcuOri;
        } catch (Exception e) {
//            e.printStackTrace();
            log.error(String.valueOf(e));
            return "";
        }
    }

    public static String calcuDouble(String calcuOri) {

        try {
            while (calcuOri.matches(".*[0-9][×*÷][0-9].*")) {
                StringBuffer sb = new StringBuffer();
                Pattern pattern = Pattern.compile("([^+*×÷-]+)([×*÷])([^+*×÷-]+)");
                Matcher m = pattern.matcher(calcuOri);
                while (m.find()) {
                    BigDecimal n1 = new BigDecimal(m.group(1));
                    BigDecimal n2 = new BigDecimal(m.group(3));
                    String res = "";
                    if (StringUtils.equals(m.group(2), "×")) {
                        res = String.valueOf(n1.multiply(n2));
                    }
                    if (StringUtils.equals(m.group(2), "*")) {
                        res = String.valueOf(n1.multiply(n2));
                    }
                    if (StringUtils.equals(m.group(2), "÷")) {
                        res = String.valueOf(n1.divide(n2));
                    }
                    m.appendReplacement(sb, res);
                }
                m.appendTail(sb);
                calcuOri = sb.toString();
            }
            while (calcuOri.matches(".*[0-9]-[0-9].*")) {
                StringBuffer sb = new StringBuffer();
                Pattern pattern = Pattern.compile("([^+*×÷-]+)([-])([^+*×÷-]+)");
                Matcher m = pattern.matcher(calcuOri);
                while (m.find()) {
                    BigDecimal n1 = new BigDecimal(m.group(1));
                    BigDecimal n2 = new BigDecimal(m.group(3));
                    String res = "";
                    if (StringUtils.equals(m.group(2), "-")) {
                        res = String.valueOf(n1.subtract(n2));
                    }
                    m.appendReplacement(sb, res);
                }
                m.appendTail(sb);
                calcuOri = sb.toString();
                calcuOri = calcuOri.replaceAll("\\+-|--|-+", "-");
            }
            while (calcuOri.matches(".*[0-9][+-][0-9].*")) {
                StringBuffer sb = new StringBuffer();
                Pattern pattern = Pattern.compile("([^+*×÷-]+)([+-])([^+*×÷-]+)");
                Matcher m = pattern.matcher(calcuOri);
                while (m.find()) {
                    BigDecimal n1 = new BigDecimal(m.group(1));
                    BigDecimal n2 = new BigDecimal(m.group(3));
                    String res = "";
                    if (StringUtils.equals(m.group(2), "+")) {
                        res = String.valueOf(n1.add(n2));
                    }
                    if (StringUtils.equals(m.group(2), "-")) {
                        res = String.valueOf(n1.subtract(n2));
                    }
                    m.appendReplacement(sb, res);
                }
                m.appendTail(sb);
                calcuOri = sb.toString();
            }
            return calcuOri;
        } catch (Exception e) {
//            e.printStackTrace();
            log.error(String.valueOf(e));
            return "";
        }
    }

    public static Fraction fraction(String group) {
        try {
            if (hasFraction(group)) {
                return new Fraction(group);
            } else {
                return new Fraction(Long.parseLong(group));
            }
        } catch (Exception e) {
            log.error(String.valueOf(e));
            return null;
        }
    }

    public static boolean hasDecimal(String math) {
        return math.matches(".*[0-9]+\\.[0-9]+.*");
    }

    public static boolean hasFraction(String math) {
        boolean a = math.matches(".*[0-9]+/[0-9]+.*");
        boolean b = math.matches(".*[0-9]+\\.[0-9]+/[0-9]+.*");
        boolean c = math.matches(".*[0-9]+/[0-9]+\\.[0-9]+.*");
        boolean d = math.matches(".*[0-9]+\\.[0-9]/[0-9]+\\.[0-9]+.*");
        return a | b | c | d;
    }

    public static boolean isNotEval(String math) {
        boolean a = math.matches(".*[-+*/]{2,}.*");
        boolean b = math.matches(".*[0-9]+\\.[0-9]+\\..*");
        boolean c = math.matches(".*[^0-9)]$");
        return a | b | c;
    }

    public static int gcd(int a, int b) {
        return b == 0 ? a : gcd(b, a % b);//利用辗转相除法求得最大公约数
    }

    public static String grade(String one, String two) {
        String resStr = "左括号" + smart(one) + "比" + smart(two) + "右括号";
        return resStr;
    }

    public static String gradePriamary(String one, String two) {
//        String resStr = smart(one) + "比" + smart(two);
        String resStr = one + "/" + two;
        return resStr;
    }

    public static String smart(String str) {
        if (isNum(str)) {
            if (str.length() > 12) {
                return str;
            }
            if (isDouble(str)) {
                return DigitalToChineseUtil.bigDecimal2chineseNum(BigDecimal.valueOf(Double.parseDouble(str)));
            }
            return DigitalToChineseUtil.bigDecimal2chineseNum(BigDecimal.valueOf(Long.parseLong(str)));
        } else {
            return str;
        }
    }

    public static String smartNorm(String str) {
        if (isNum(str)) {
            if (str.length() > 12) {
                return str;
            }
            if (isDouble(str)) {
                return DigitalToChineseUtil.bigDecimal2chineseNumNorm(BigDecimal.valueOf(Double.parseDouble(str)));
            }
            return DigitalToChineseUtil.bigDecimal2chineseNumNorm(BigDecimal.valueOf(Long.parseLong(str)));
        } else {
            return str;
        }
    }

    public static String smartPrimary(String str) {
        if (isNum(str)) {
            if (str.length() > 12) {
                return str;
            }
            return doubleOrLong(str);
        } else {
            if (hasFraction(str)) {
                String[] split = str.split("/");
                String one = doubleOrLong(split[0]).replaceAll("&.*数", "");
                String two = doubleOrLong(split[1]).replaceAll("&.*数", "");
                return one + "比" + two + "&分数";
            }
        }
        return str;
    }

    public static String doubleOrLong(String str) {
        if (isNum(str)) {
            if (isDouble(str)) {
//                return DigitalToChineseUtil.bigDecimal2chineseNumPrimary(BigDecimal.valueOf(Double.parseDouble(str)));
                return DigitalToChineseUtil.bigDecimal2chineseNumPrimary(new BigDecimal(str));
            }
            if (str.length() > 12) {
                return str;
            }
            return DigitalToChineseUtil.bigDecimal2chineseNumPrimary(BigDecimal.valueOf(Long.parseLong(str)));
        }
        return str;

    }

    public static boolean isNum(String str) {
        return str.matches("(^[0-9]+$)|(^[0-9]+\\s*\\.\\s*[0-9]+$)");
    }

    public static boolean isLong(String str) {
        return str.matches("^[0-9]+$");
    }

    public static boolean isDouble(String str) {
        return str.matches("^[0-9]+\\s*\\.\\s*[0-9]+$");
    }

    public static String calculate(String math) {
        String[] maths = math.split("[+×÷=-]");
        for (String s : maths) {
            math = math.replaceFirst(s, smart(s));
        }
        return math;
    }

    public static String calculateNorm(String math) {
        String[] maths = math.split("[+×÷=-]");
        for (String s : maths) {
            math = math.replaceFirst(s, smartNorm(s));
        }
        return math;
    }

    public static String calculatePrimary(String math) {
        String[] maths = math.split("[+*×÷=()-]");
        for (String s : maths) {
            math = math.replaceFirst(s, smartPrimary(s));
        }
        return math;
    }


    public static void main(String[] args) throws Exception {

//        String so = "2/4+4*3*1-20+6÷2";
//        String s = "2/4+4*3*1-20+6/2";
//        System.out.println(calcuRes(so, s));
        String r = "12.000001";
        r = r.replaceAll("0+$", "0");
        Fraction f = new Fraction("21/3");
        System.out.println(f);
//
//        int decimal;
//        int a, b, c;
//        int g;
//        a = 0;//整数部分
//        decimal = 1333333333;//小数部分
//        int length = String.valueOf(decimal).length();
//        b = decimal;
//        c = (int) Math.pow(10, length);
//        g = gcd(b, c);
//        System.out.println(a + " " + b / g + " " + c / g);
//        System.out.println(isDouble("938902  . 323"));
//        System.out.println(isDouble("23455224425242424222445224545244222525222222252242938902  . 323"));
//        System.out.println(isLong("93872"));
//        System.out.println(isNum("3fw33.424"));
//        System.out.println(isNum("93872"));
//        System.out.println(isNum("938902  . 323"));
//
//        ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
//        ScriptEngine scriptEngine = scriptEngineManager.getEngineByName("nashorn");
        String expression = "12+9*1.3-(2/4)";
//
        try {
            BigDecimal a = new BigDecimal("1");
            BigDecimal b = new BigDecimal("2");
            System.out.println(a.divide(b));
            System.out.println(a.divide(b, 4, RoundingMode.DOWN));
//            System.out.println(new BigDecimal("13").divide(new BigDecimal("3")).setScale(2, RoundingMode.DOWN).doubleValue());
            System.out.println(new BigDecimal("133").divide(new BigDecimal("3"), RoundingMode.CEILING));
            System.out.println(new BigDecimal("13").divide(new BigDecimal("3"), RoundingMode.DOWN));
            System.out.println(new BigDecimal("13").divide(new BigDecimal("3"), RoundingMode.HALF_DOWN));
            System.out.println(new BigDecimal("133").divide(new BigDecimal("3"), RoundingMode.HALF_UP));
            System.out.println(new BigDecimal("31").divide(new BigDecimal("3"), RoundingMode.HALF_EVEN));
//            System.out.println(new BigDecimal("1").divide(new BigDecimal("3"), RoundingMode.UNNECESSARY));
            System.out.println(new BigDecimal("1").divide(new BigDecimal("3"), RoundingMode.UP));
            System.out.println(calcuRes(expression, expression));
            System.out.println(5 * 0.17);
            System.out.println(new BigDecimal("0.17").multiply(new BigDecimal("5")));
            System.out.println(new BigDecimal("0.17").subtract(new BigDecimal("5")));
            System.out.println(new BigDecimal("0.17").divide(new BigDecimal("5")));
            System.out.println(new BigDecimal("0.17").add(new BigDecimal("5")));
//            System.out.println(calcuDouble(expression));
//            System.out.println(calcuFraction(expression));
            String result1 = String.valueOf(scriptEngine.eval(expression));
//            BigDecimal result = BigDecimal.valueOf((Double) scriptEngine.eval(expression));
            System.out.println(result1);
//            System.out.println(result);
        } catch (ScriptException e) {
            e.printStackTrace();
        }
//        String content = "\\textcircled{1}一  、1、23455, 987,1  ——————,()-(测试话你就二 )；\\frac{1}{5}（中文的）≈;＝、1+1=2-4=  9÷3=  6×90   = ,（  ） A. B.（1分） (1  分) (1   分  ) (    1 分 )   .a., (1)。\u200b,\u200c,n  amea, \\frac { 2 } { 6 } - \\frac { 2 } { 6 } = 0   (1).";
//        String content = "\\beginsplit&4 2 + 8 = 3 3 6,4 2.887 + 8 = ,0.45,67,2  .   7 -  0.   7 =  ";
//        String content = "1 .8+4.0+3=123  ,,,,.0 .4 +9.6 - 6 × 10 = (5+7)+9.0;9+8+7=四十七&两位1000数加二十八&两位数\\textcircled七十四&两位数20.45 ";
//        String content = "4 .  1 小明和小华分别从一座桥的两端同时出发\\textcircled{>},往返于桥的两端之间,小明的速度是65米/分,小华的速度是75米/分,经过15分钟两人第二次相遇,这座桥长多少米?(4分)";
//        String content = "1+2+3+4++2022=1+2+3+4++2022=1+2+3+4+2022=1+2+3+4--2022=1+2+3+4++2022=";
//        content = "23.9-10.5-1.215.3+12.8-2.6=";
//        content = "2.+=";
//        System.out.println(content);
//        System.out.println(regexp(content));
        String content = "\\begin{xxx}2/5*6=name\\end{xxx}\\begin{xxx}2.\\end{xxx}\\begin{xxx}yyg.\\end{xxx}\\begin{xxx}namevvd.\\end{xxx}";
        System.out.println(regexpPrimary(content));
    }
}
