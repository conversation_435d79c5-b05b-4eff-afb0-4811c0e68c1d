package com.iflytek.rec.anchorpredic.method.lhsvote;

import static java.lang.Math.abs;

public class DigitUtil {
    private DigitUtil()
    {
        throw new IllegalStateException("Utility class");
    }
    /**
     * 浮点数比较精度
     */
    private static final double PRECISION = 0.000001;

    /**
     * 判断浮点数不相等,返回true即不想等
     * @param a 输入double
     * @param b 输入double
     * @return  是否相等
     */
    public static boolean isNotEqual(double a,double b) {
        return abs(a-b) > PRECISION;
    }
}
