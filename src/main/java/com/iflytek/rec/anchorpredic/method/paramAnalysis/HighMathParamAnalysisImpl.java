package com.iflytek.rec.anchorpredic.method.paramAnalysis;

import com.iflytek.rec.anchorpredic.interfaces.IParamAnalysis;
import com.iflytek.rec.anchorpredic.method.AnalysLatexUMap;
import com.iflytek.rec.anchorpredic.method.regexp.CommenRegExp;

import java.io.IOException;
import java.text.Normalizer;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2023/1/3 15:18
 */
public class HighMathParamAnalysisImpl implements IParamAnalysis {
    @Override
    public String paramAnalysisHtml(String htmlString, String resourcePattern) throws IOException {
        // 去除不可见字符
        htmlString = removeISOControlCharacters(htmlString);
        // unicode转换
        htmlString = (Normalizer.isNormalized(htmlString, Normalizer.Form.NFKC)) ? htmlString : Normalizer.normalize(htmlString, Normalizer.Form.NFKC);
        // 上标解析
        htmlString = htmlString.replaceAll("<sup.*?>(.*?)<\\/sup>", "^$1");
        htmlString = htmlString.replaceAll("&sup(.*?)", "^$1");
        // 其他标签解析
        htmlString = AnalysLatexUMap.latexMapOptPhysics(htmlString, "ifly-latex-begin(?<mathStr>.*?)ifly-latex-end", resourcePattern);
        // html解析
        htmlString = AnalysLatexUMap.analysLatexUMapPhysics(htmlString, resourcePattern);
        // 解析未在标签中的latex
        htmlString = AnalysLatexUMap.latex(htmlString, resourcePattern);
        //
        htmlString = CommenRegExp.regexpHighMath(htmlString);
        return htmlString;
    }

    @Override
    public String paramAnalysisOcr(String ocrString, String resourcePattern){
        ocrString = removeISOControlCharacters(ocrString);
        // unicode转换
        ocrString = (Normalizer.isNormalized(ocrString, Normalizer.Form.NFKC)) ? ocrString : Normalizer.normalize(ocrString, Normalizer.Form.NFKC);
        // 上标解析
        ocrString = ocrString.replaceAll("<sup.*?>(.*?)<\\/sup>", "^$1");
        ocrString = ocrString.replaceAll("&sup(.*?)", "^$1");
        // 其他标签解析
        ocrString = AnalysLatexUMap.latexMapOptPhysics(ocrString, "ifly-latex-begin(?<mathStr>.*?)ifly-latex-end", resourcePattern);
        // html解析
        // ocrString = AnalysLatexUMap.analysLatexUMapPhysics(ocrString, resourcePattern);
        // 解析未在标签中的latex
        ocrString = AnalysLatexUMap.latex(ocrString, resourcePattern);
        //
        ocrString = CommenRegExp.regexpHighMath(ocrString);
        return ocrString;

    }

    public String removeISOControlCharacters(String input) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            if (!Character.isISOControl(input.charAt(i))) {
                result.append(input.charAt(i));
            }
        }
        return result.toString();
    }

}
