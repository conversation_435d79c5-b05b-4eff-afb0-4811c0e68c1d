package com.iflytek.rec.anchorpredic.method.paramAnalysis;

import com.iflytek.rec.anchorpredic.interfaces.IParamAnalysis;
import com.iflytek.rec.anchorpredic.utils.Constant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2023/1/3 15:24
 */
public class ParamAnalysisHolder {
    private static final Logger log = LoggerFactory.getLogger(ParamAnalysisHolder.class);
    private static final IParamAnalysis juniorParam = new JuniorParamAnalysisImpl();
    private static final IParamAnalysis juniorPhysicsParam = new JuniorPhysicsParamAnalysisImpl();
    private static final IParamAnalysis highMathParam = new HighMathParamAnalysisImpl();
    private static final IParamAnalysis primaryParam = new PrimaryParamAnalysisImpl();

    public static String of(String pattern, String strType, String topicStr) {
        try {
            if (StringUtils.equals(pattern, Constant.PRIMARY_PATTERN)) {
                if (StringUtils.equals(strType, Constant.TOPIC_OCR_TYPE)) {
                    return primaryParam.paramAnalysisOcr(topicStr, pattern);
                }
                return primaryParam.paramAnalysisHtml(topicStr, pattern);
            } else if (StringUtils.equals(pattern, Constant.JUNIOR_PATTERN)) {
                if (StringUtils.equals(strType, Constant.TOPIC_OCR_TYPE)) {
                    return juniorParam.paramAnalysisOcr(topicStr, pattern);
                }
                return juniorParam.paramAnalysisHtml(topicStr, pattern);
            } else if (StringUtils.equals(pattern, Constant.JUNIOR_PHYSICS_PATTERN)) {
                if (StringUtils.equals(strType, Constant.TOPIC_OCR_TYPE)) {
                    return juniorPhysicsParam.paramAnalysisOcr(topicStr, pattern);
                }
                return juniorPhysicsParam.paramAnalysisHtml(topicStr, pattern);
            } else if (StringUtils.equals(pattern, Constant.HIGH_MATH_PATTERN)) {
                if (StringUtils.equals(strType, Constant.TOPIC_OCR_TYPE)) {
                    return highMathParam.paramAnalysisOcr(topicStr, pattern);
                }
                return highMathParam.paramAnalysisHtml(topicStr, pattern);
            } else {
                log.error("传入学科学段:{},非小学数学或初中数学或初中物理，获取不到参数解析规则", pattern);
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("传入学科学段:{},非小学数学或初中数学或初中物理，获取参数解析规则出错={}", pattern, e.toString());
            return null;
        }
    }
}
