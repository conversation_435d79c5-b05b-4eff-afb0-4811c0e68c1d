package com.iflytek.rec.anchorpredic.method;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/9/26 15:41
 */
public class OriginalLatex {
    public static StringBuilder originalLatex(String topicbody) throws IOException {
        /*
         * 深度遍历取出试题题干信息
         * 未解析latex版本
         * */
        Document document = Jsoup.parse(topicbody, "UTF-8");
        Elements html = document.getElementsByTag("html");
        List<Node> p_nodes = new ArrayList<>(html.get(0).childNodes());
        StringBuilder topic_body = new StringBuilder();
        while (p_nodes.size() != 0) {
            Node p_node = p_nodes.get(p_nodes.size() - 1);
            if (p_node.childNodes().size() != 0) {
                p_nodes.remove(p_nodes.size() - 1);
                p_nodes.addAll(p_node.childNodes());
            }
            if (p_node.childNodes().size() == 0) {
                if (p_node.hasAttr("data-latex")) {
                    String Res1 = URLDecoder.decode(p_node.attr("data-latex"), "UTF-8");
                    topic_body.insert(0, Res1);
                    p_nodes.remove(p_nodes.size() - 1);
                } else {
                    if (p_node.attributes().size() == 1 && !p_node.attributes().hasKey("src")) {
                        topic_body.insert(0, p_node);
                        p_nodes.remove(p_nodes.size() - 1);
                    } else {
                        p_nodes.remove(p_nodes.size() - 1);
                    }
                }
            }

        }
        return topic_body;
    }
}
