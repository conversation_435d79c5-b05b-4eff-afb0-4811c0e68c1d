package com.iflytek.rec.anchorpredic.method.lhsvote;

import com.iflytek.rec.anchorpredic.pojo.lhsvote.PointType;
import org.apache.commons.lang.StringUtils;

public class ConstantValidlateUtil {

    private ConstantValidlateUtil()
    {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 校验拍搜画像-点类型是否合法
     * 当前支持点类型：锚点
     * @param pointType 点类型
     * @return  true：拍搜画像支持点类型  false：拍搜画像不支持点类型
     */
    public static boolean validatePhotoPointType(String pointType){
        //拍搜画像是否支持
        boolean legal = false;
        if (StringUtils.isNotEmpty(pointType) && pointType.equals(PointType.ANCHOR_POINT)){
            legal = true;
        }

        return legal;
    }
}
