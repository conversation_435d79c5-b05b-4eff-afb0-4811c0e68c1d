package com.iflytek.rec.anchorpredic.method;

import com.iflytek.rec.anchorpredic.utils.DigitalToChineseUtil;
import com.iflytek.rec.anchorpredic.datafactory.holder.FileReaderUtil;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.parser.Tag;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.iflytek.rec.anchorpredic.method.regexp.CommenRegExp.*;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/9/26 15:20
 */
public class AnalysLatexUMap {
    private static final String PATTERN_FS = "\\{\\d+\\}";

    private static final Pattern pFenShu = Pattern.compile(PATTERN_FS);

    public static StringBuilder analysLatexUMap(String topicbody, String resourcePattern) throws IOException {
        /*
         * 深度遍历取出试题题干信息
         * 使用map映射表解析latex版本
         * */
        Document document = Jsoup.parse(topicbody, "UTF-8");
        Elements html = document.getElementsByTag("html");
        List<Node> p_nodes = new ArrayList<>(html.get(0).childNodes());
        StringBuilder topic_body = new StringBuilder();
        while (p_nodes.size() != 0) {
            Node p_node = p_nodes.get(p_nodes.size() - 1);
            if (p_node.childNodes().size() != 0) {
                p_nodes.remove(p_nodes.size() - 1);
                p_nodes.addAll(p_node.childNodes());
            }
            if (p_node.childNodes().size() == 0) {
                if (p_node.hasAttr("data-latex")) {
                    String res1 = URLDecoder.decode(p_node.attr("data-latex"), "UTF-8");
                    String res2 = latexMapOpt(res1, resourcePattern);
                    topic_body.insert(0, res2);
                    p_nodes.remove(p_nodes.size() - 1);
                } else {
                    if (p_node.attributes().size() == 1 && !p_node.attributes().hasKey("src")) {
                        if (!isNotInsert(p_node)) {
                            topic_body.insert(0, smart(String.valueOf(p_node)));
                        }
                        p_nodes.remove(p_nodes.size() - 1);
                    } else {
                        p_nodes.remove(p_nodes.size() - 1);
                    }
                }
            }

        }
        return topic_body;
    }

    public static StringBuilder analysLatexUMapPrimary(String topicbody, String resourcePattern) throws IOException {
        /*
         * 深度遍历取出试题题干信息
         * 使用map映射表解析latex版本
         * */
        Document document = Jsoup.parse(topicbody, "UTF-8");
        Elements html = document.getElementsByTag("html");
        List<Node> p_nodes = new ArrayList<>(html.get(0).childNodes());
        StringBuilder topic_body = new StringBuilder();
        while (p_nodes.size() != 0) {
            Node p_node = p_nodes.get(p_nodes.size() - 1);
            if (p_node.childNodes().size() != 0) {
                p_nodes.remove(p_nodes.size() - 1);
                p_nodes.addAll(p_node.childNodes());
            }
            if (p_node.childNodes().size() == 0) {
                if (p_node.hasAttr("data-latex")) {
                    String res1 = URLDecoder.decode(p_node.attr("data-latex"), "UTF-8");
                    String res2 = latexMapOptPrimary(res1, resourcePattern);
                    topic_body.insert(0, res2);
                    p_nodes.remove(p_nodes.size() - 1);
                } else {
                    if (p_node.attributes().size() == 1 && !p_node.attributes().hasKey("src")) {
                        if (!isNotInsert(p_node)) {
                            topic_body.insert(0, smartPrimary(String.valueOf(p_node)));
                        }
                        p_nodes.remove(p_nodes.size() - 1);
                    } else {
                        p_nodes.remove(p_nodes.size() - 1);
                    }
                }
            }

        }
        return topic_body;
    }

    public static String analysLatexUMapPhysics(String topicbody, String resourcePattern) throws IOException {
        Document textByHtml = Jsoup.parse(topicbody, "UTF-8", org.jsoup.parser.Parser.xmlParser());
        Elements elements = textByHtml.select("img");

        for (Element e : elements) {
            String dataLatex = e.attr("data-latex");
            String decodedLatex;
            if (dataLatex != null && !dataLatex.isEmpty()) {
                decodedLatex = latex(java.net.URLDecoder.decode(dataLatex, "UTF-8"), resourcePattern);
            } else {
                decodedLatex = " ";
            }
            e.replaceWith(new Element(Tag.valueOf("span"), "UTF-8").text(decodedLatex));
        }

        return textByHtml.text();
    }

    private static boolean isNotInsert(Node p_node) {
        String pNodeStr = String.valueOf(p_node);
        boolean e = pNodeStr.matches("^<.*?/[a-z]>$");
        boolean a = StringUtils.equals(p_node.attributes().get("style"), "cursor:pointer");
        boolean b = StringUtils.equals(p_node.attributes().get("class"), "DefaultParagraph");
        boolean c = StringUtils.equals(p_node.attributes().get("class"), "blank blank1 _blank_1");
        boolean d = StringUtils.equals(p_node.attributes().get("class"), "_blank_1");
        boolean f = StringUtils.equals(p_node.attributes().get("data-filtered"), "filtered");
        return a || b || c || d || e||f;
    }

    public static String latexMap(String latexCode, String resourcePattern) {
        for (Map.Entry<String, String> entry : FileReaderUtil.of(resourcePattern).getLatexToTextMap().entrySet()) {
            if (latexCode.contains(entry.getKey())) {
                //分数特殊处理\\frac{1}{5}一
                if (StringUtils.equals(entry.getKey(), "\\frac")) {
                    latexCode = latexCode.replace(entry.getKey(), entry.getValue());
                    Matcher m = pFenShu.matcher(latexCode);
                    StringBuilder sb = new StringBuilder();
                    int i = 0;
                    while (m.find()) {
                        if (i == 0) {
                            sb.append(DigitalToChineseUtil.bigDecimal2chineseNum(BigDecimal.valueOf(Double.parseDouble(m.group().replace("{", "").replace("}", "")))));
                            sb.append(entry.getValue());
                            i++;
                        } else {
                            sb.append(DigitalToChineseUtil.bigDecimal2chineseNum(BigDecimal.valueOf(Double.parseDouble(m.group().replace("{", "").replace("}", "")))));
                        }
                    }
                    latexCode = sb.toString();

                } else {
                    if (entry.getValue() == null) {
//                        System.out.println("null-latex:"+entry.getKey());
                        continue;
                    }
                    latexCode = latexCode.replace(entry.getKey(), entry.getValue());
                }
            }
        }
        return latexCode;
    }

    public static String latex(String latexCode, String resourcePattern) {
        for (Map.Entry<String, String> entry : FileReaderUtil.of(resourcePattern).getLatexToTextMap().entrySet()) {
            if (latexCode.contains(entry.getKey())) {
                if (entry.getValue() == null) {
                    continue;
                }
                latexCode = latexCode.replace(entry.getKey(), entry.getValue());
            }
        }
        return latexCode;
    }

    public static String latexMapOpt(String latexCode, String resourcePattern) {
//        latexCode = latexCode.replaceAll("\\\\sqrt\\[3\\]\\{(.*)\\}","￥$1");
        latexCode = latex(latexCode, resourcePattern);
        //9.分数提取 分数特殊处理\\frac{1}{5}一  注意：因\\frac特殊处理 需要删除latex2text中的map关系
        Pattern pBody = Pattern.compile("\\\\frac\\s*\\{(.*?)\\}\\s*\\{(.*?)\\}");
        Matcher m = pBody.matcher(latexCode);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            m.appendReplacement(sb, grade(m.group(1), m.group(2)));
        }
        m.appendTail(sb);
        latexCode = sb.toString();

        return latexCode;
    }

    public static String latexMapOptPrimary(String latexCode, String resourcePattern) {
//        latexCode = latexCode.replaceAll("\\\\sqrt\\[3\\]\\{(.*)\\}","￥$1");
        latexCode = latex(latexCode, resourcePattern);
        //9.分数提取 分数特殊处理\\frac{1}{5}一  注意：因\\frac特殊处理 需要删除latex2text中的map关系
        Pattern pBody = Pattern.compile("\\\\frac\\s*\\{(.*?)\\}\\s*\\{(.*?)\\}");
        Matcher m = pBody.matcher(latexCode);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            m.appendReplacement(sb, gradePriamary(m.group(1), m.group(2)));
        }
        m.appendTail(sb);
        latexCode = sb.toString();

        return latexCode;
    }

    public static String latexMapOptPhysics(String latexCode, String regex, String resourcePattern) {
        Pattern paBody = Pattern.compile(regex);
        Matcher ma = paBody.matcher(latexCode);
        StringBuffer sbf = new StringBuffer();
        while (ma.find()) {
            String str = latex(ma.group(1), resourcePattern);
            str = str.replaceAll("\\\\+", "\\\\\\\\");
            ma.appendReplacement(sbf, str);
        }
        ma.appendTail(sbf);
        return sbf.toString();
    }


    public static void main(String[] args) throws UnsupportedEncodingException {
        String res1 = URLDecoder.decode("%5Csqrt%5B3%5D%7Ba%7D", "UTF-8");
        System.out.println(res1);
        String s = "%5Csqrt%5B3%5D%7Ba%7D";
        String res = AnalysLatexUMap.latexMapOpt(res1, "");
        System.out.println(res);
    }
}