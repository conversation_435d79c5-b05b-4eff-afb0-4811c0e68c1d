package com.iflytek.rec.anchorpredic.method.lhsvote;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;

public class ValidationUtils {
    private ValidationUtils(){
        throw new IllegalStateException("Utility class");
    }

    /**
     * 校验实例
     */
    private static final Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 校验方法
     * @param request   请求
     * @param <T>   请求类型
     * @return  校验错误信息
     */
    public static <T> StringBuilder validateEntity(T request) {
        StringBuilder errMessage = new StringBuilder();

        Set<ConstraintViolation<T>> violations = validator.validate(request);

        for (ConstraintViolation<T> violation : violations) {
            String validMsg = violation.getMessage();
            errMessage.append(validMsg);
            errMessage.append(";");
        }

        return errMessage;
    }

}
