package com.iflytek.rec.anchorpredic.method;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/9/26 15:24
 */
public class MathMlToStr {
    public static StringBuilder mathMlToStr(String topicbody) throws IOException {
        /*
         * 深度遍历取出mathMl公式信息
         * 解析mathMl
         * */
        Document document = Jsoup.parse(topicbody, "UTF-8");
        Elements html = document.getElementsByTag("math");
        List<Node> p_nodes = new ArrayList<>(html.get(0).childNodes());
        StringBuilder topic_body = new StringBuilder();
        while (p_nodes.size() != 0) {
            Node p_node = p_nodes.get(p_nodes.size() - 1);
            if (p_node.childNodes().size() != 0) {
                p_nodes.remove(p_nodes.size() - 1);
                p_nodes.addAll(p_node.childNodes());
            }
            if (p_node.childNodes().size() == 0) {
                topic_body.insert(0, p_node);
                p_nodes.remove(p_nodes.size() - 1);
            }

        }
//        System.out.println("深度遍历取出mathMl公式信息:" + topic_body + "\n");
        return topic_body;
//        System.out.println("--------------------------------------------------------");

    }
}
