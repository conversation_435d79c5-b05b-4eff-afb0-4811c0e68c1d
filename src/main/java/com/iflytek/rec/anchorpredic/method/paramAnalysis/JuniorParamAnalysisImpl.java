package com.iflytek.rec.anchorpredic.method.paramAnalysis;

import com.iflytek.rec.anchorpredic.impl.AnchorPredicMethodImpl;
import com.iflytek.rec.anchorpredic.interfaces.IParamAnalysis;
import com.iflytek.rec.anchorpredic.method.AnalysLatexUMap;
import com.iflytek.rec.anchorpredic.method.regexp.CommenRegExp;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.iflytek.rec.anchorpredic.method.AnalysLatexUMap.latex;
import static com.iflytek.rec.anchorpredic.method.AnalysLatexUMap.latexMapOpt;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2023/1/3 15:18
 */
public class JuniorParamAnalysisImpl implements IParamAnalysis {
    @Override
    public String paramAnalysisHtml(String htmlString, String resourcePattern) throws IOException {
        htmlString = htmlString.replaceAll("\\uD835\\uDF0B", "π");
        htmlString = htmlString.replaceAll("&nbsp;|&nbsp|\\\\textcelsius", "");
        htmlString = htmlString.replaceAll("\\\\endmatrix|\\\\beginmatrix", "");
        StringBuilder topic_body = AnalysLatexUMap.analysLatexUMap(htmlString, resourcePattern);
//        去除题号和得分  保留中间题干
        StringBuffer sb = CommenRegExp.regexp(String.valueOf(topic_body));
        //整数和小数转为汉字
        StringBuffer sb1 = AnchorPredicMethodImpl.patternNumToWorld(sb);
        //+-×÷=映射为汉字：加减乘除等于
        String sb2 = AnchorPredicMethodImpl.patternOperatorToWorld(sb1);
        sb2 = AnchorPredicMethodImpl.patternFinalProcess(sb2);
        return sb2;
    }

    @Override
    public String paramAnalysisOcr(String ocrString, String resourcePattern) {
        ocrString = ocrString.replaceAll("\\uD835\\uDF0B", "π");
        ocrString = ocrString.replaceAll("\\\\pi", "π");
        ocrString = ocrString.replaceAll("&nbsp;|&nbsp|\\\\cdots|\\\\textcelsius", "");
        ocrString = ocrString.replaceAll("\\\\endmatrix|\\\\beginmatrix", "");
        ocrString = latex(ocrString, resourcePattern);
        Pattern paBody = Pattern.compile("ifly-latex-begin(.+)ifly-latex-end");
        Matcher ma = paBody.matcher(ocrString);
        StringBuffer sbf = new StringBuffer();
        while (ma.find()) {
            String str = latexMapOpt(ma.group(1), resourcePattern);
            str = str.replaceAll("\\\\+", "\\\\\\\\");
            ma.appendReplacement(sbf, str);
        }
        ma.appendTail(sbf);
        String ocrLatexAnalysis = sbf.toString();
        //去除题号和得分  保留中间题干
        StringBuffer sb = CommenRegExp.regexp(ocrLatexAnalysis);
        //整数和小数转为汉字
        StringBuffer sb1 = AnchorPredicMethodImpl.patternNumToWorld(sb);
        //+-×÷=映射为汉字：加减乘除等于
        String sb2 = AnchorPredicMethodImpl.patternOperatorToWorld(sb1);
        sb2 = AnchorPredicMethodImpl.patternFinalProcess(sb2);
        return sb2;
    }
}
