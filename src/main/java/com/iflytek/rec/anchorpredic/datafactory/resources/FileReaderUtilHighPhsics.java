package com.iflytek.rec.anchorpredic.datafactory.resources;

import com.alibaba.fastjson2.JSONArray;
import com.iflytek.rec.anchorpredic.datafactory.holder.IResourceHolder;
import com.iflytek.rec.anchorpredic.pojo.AnchorInfo;
import com.iflytek.rec.anchorpredic.pojo.WordToIndex;
import com.iflytek.rec.anchorpredic.utils.Constant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/10/8 16:39
 */
public class FileReaderUtilHighPhsics implements IResourceHolder {
    private static final Logger log = LoggerFactory.getLogger(FileReaderUtilHighPhsics.class);

    private static final Map<Integer, HashSet<String>> anchorIdToIndexs = new HashMap<>();

    /**
     * anchorID与章节映射关系
     */
    private static final Map<String, String> anchorIdToChapter = new HashMap<>();

    /**
     * anchorID与教材版本映射关系
     */
    private static final Map<String, String> anchorIdToBookversion = new HashMap<>();

    /**
     * anchorID与学期映射关系
     */
    private static final Map<String, String> anchorIdToSemester = new HashMap<>();
    /**
     * anchorID与锚点名称关系
     */
    private static final Map<String, String> anchorIdToName = new HashMap<>();
    /**
     *
     */
    private static final Map<String, List<String>> anchorIdRepeat = new HashMap<>();

    private static final Map<String, String> BookVersionMap = new HashMap<>();
    /**
     * 统计词表index对应关系
     */
    private static final Map<String, Integer> wordToIndexs = new HashMap<>();

    private static final Map<Integer, HashSet<String>> indexToAnchorIdRepeat = new HashMap<>();
    private static final IResourceHolder instance = InstanceHolder.instance;

    FileReaderUtilHighPhsics() {
    }

    //todo point_info表和wordToIndex【word2index转换一下】表需要替换成初物的
    static {
        FileReaderUtilHighPhsics f = new FileReaderUtilHighPhsics();
        try {
            for (String s : f.FileReader("studydata/highphsics/point_info.json")) {
                List<AnchorInfo> anchorInfoList = JSONArray.parseArray(s, AnchorInfo.class);
                int i = 0;
                for (AnchorInfo anchorInfo : anchorInfoList) {
                    i++;
                    StringBuilder st = new StringBuilder();
                    st.append(i);
                    //数据魔法值  锚点前加上POINT_MAGICNUM位（当前锚点数量8000+,如果增加为10000+,则加上5位,后续结果相应去除）编号 重编码
                    while (st.length() != Constant.POINT_MAGICNUM) {
                        st.insert(0, "0");
                    }
                    anchorIdToIndexs.computeIfAbsent(anchorInfo.getIndex(), k -> new HashSet<>());
                    anchorIdToIndexs.get(anchorInfo.getIndex()).add(anchorInfo.getId());
                    anchorIdToName.put(st.toString() + anchorInfo.getId(), anchorInfo.getName());
                    anchorIdToBookversion.put(st.toString() + anchorInfo.getId(), anchorInfo.getEdition());
                    anchorIdToChapter.put(st.toString() + anchorInfo.getId(), anchorInfo.getChapter_code());
                    anchorIdToSemester.put(st.toString() + anchorInfo.getId(), Constant.GRADE[Math.min(Constant.NOEXIST, anchorInfo.getGrade())] + "年级" + anchorInfo.getBook_version());

                    BookVersionMap.put(anchorInfo.getEdition(), anchorInfo.getEdition_name());

                }
                log.info("highphsics-point_info.json size ,{} ,anchorIds cache success! Size:{}", i, anchorIdToIndexs.size());
                break;

            }
            for (String anchor : anchorIdToBookversion.keySet()) {
                if (anchorIdRepeat.get(anchor.substring(Constant.POINT_MAGICNUM)) == null) {
                    anchorIdRepeat.put(anchor.substring(Constant.POINT_MAGICNUM), new ArrayList<>());
                    anchorIdRepeat.get(anchor.substring(Constant.POINT_MAGICNUM)).add(anchor);
                } else {
                    anchorIdRepeat.get(anchor.substring(Constant.POINT_MAGICNUM)).add(anchor);
                }
            }
            for (String s : f.FileReader("studydata/highphsics/wordToIndex.json")) {
                List<WordToIndex> wordToIndices = JSONArray.parseArray(s, WordToIndex.class);
                for (WordToIndex wordToIndex : wordToIndices) {
                    wordToIndexs.put(wordToIndex.getWord(), wordToIndex.getIndex());
                }
                log.info("highphsics-wordToIndex.json cache success! Size:{}", wordToIndexs.size());
                break;
            }
            for (int i = 0; i < anchorIdToIndexs.size(); i++) {
                indexToAnchorIdRepeat.computeIfAbsent(i, k -> new HashSet<>());
                Set<String> anchorIds = anchorIdToIndexs.get(i);
                for (String anchor : anchorIds) {
                    indexToAnchorIdRepeat.get(i).addAll(anchorIdRepeat.get(anchor));
                }
            }
        } catch (Exception e) {
            log.error("highphsics-point_info.json cache failed!, {}", e.toString());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    public static IResourceHolder of() {
        return instance;
    }

    static class InstanceHolder {
        static IResourceHolder instance = new FileReaderUtilHighPhsics();
    }

    @Override
    public Map<String, List<String>> getAnchorIdRepeat() {
        return anchorIdRepeat;
    }

    @Override
    public Map<String, String> getBookVersionMap() {
        return BookVersionMap;
    }

    @Override
    public Map<String, String> getAnchorIdToChapter() {
        return anchorIdToChapter;
    }

    @Override
    public Map<String, String> getAnchorIdToBookversion() {
        return anchorIdToBookversion;
    }

    @Override
    public Map<String, String> getAnchorIdToSemester() {
        return anchorIdToSemester;
    }

    @Override
    public Map<String, String> getAnchorIdToName() { return anchorIdToName;}

    @Override
    public Map<Integer, HashSet<String>> getAnchorIdToIndexs() {
        return anchorIdToIndexs;
    }

    @Override
    public Map<String, Integer> getWordToIndexs() {
        return wordToIndexs;
    }

    @Override
    public Map<Integer, HashSet<String>> getIndexToAnchorIdRepeat() {
        return indexToAnchorIdRepeat;
    }

    public static void main(String[] args) {
        FileReaderUtilHighPhsics junior = new FileReaderUtilHighPhsics();
        System.out.println(junior.getAnchorIdToIndexs());
        int i = 0;
        for (Map.Entry<Integer, HashSet<String>> entry : junior.getAnchorIdToIndexs().entrySet()) {
            if (entry.getValue().size() == 1) {
                continue;
            }
            System.out.println(entry.getKey() + "_" + entry.getValue().size());
            i++;
        }
        System.out.println("--------------");
        System.out.println(i);
    }
}
