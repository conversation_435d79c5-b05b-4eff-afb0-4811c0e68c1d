package com.iflytek.rec.anchorpredic.datafactory.holder;

import com.google.common.io.Resources;

import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/12/4 17:31
 */
public interface IResourceHolder {


    /**
     * latex语法翻译映射关系
     */
    Map<String, String> latexToTextMap = new LinkedHashMap<>();

    default Map<String, String> getLatexToTextMap() {
        return latexToTextMap;
    }

    Map<String, List<String>> getAnchorIdRepeat();

    Map<String, String> getBookVersionMap();

    Map<String, String> getAnchorIdToChapter();

    Map<String, String> getAnchorIdToBookversion();

    Map<String, String> getAnchorIdToSemester();

    Map<String, String> getAnchorIdToName();

    Map<Integer, HashSet<String>> getAnchorIdToIndexs();

    Map<String, Integer> getWordToIndexs();

    Map<Integer, HashSet<String>> getIndexToAnchorIdRepeat();

    /**
     * 读取resources下文件内容并转换成List
     */
    default List<String> FileReader(String fileName) throws Exception {
        URL url = Resources.getResource(fileName).toURI().toURL();
        List<String> list = Resources.readLines(url, StandardCharsets.UTF_8);
        return list;
    }
}
