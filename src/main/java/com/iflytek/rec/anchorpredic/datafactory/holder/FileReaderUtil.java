package com.iflytek.rec.anchorpredic.datafactory.holder;

import com.alibaba.fastjson2.JSONArray;
import com.google.common.io.Resources;
import com.iflytek.rec.anchorpredic.datafactory.resources.*;
import com.iflytek.rec.anchorpredic.pojo.AnchorPredictParams;
import com.iflytek.rec.anchorpredic.pojo.BookAnchorMap;
import com.iflytek.rec.anchorpredic.pojo.LatexToText;
import com.iflytek.rec.anchorpredic.utils.Constant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/10/8 16:39
 */
public class FileReaderUtil implements IResourceHolder {
    private static final Logger log = LoggerFactory.getLogger(FileReaderUtil.class);


    private static final IResourceHolder instance = FileReaderUtil.InstanceHolder.instance;


    static {
        FileReaderUtil f = new FileReaderUtil();
        try {
            for (String s : f.FileReader("model/latexmap/latex2text.json")) {
                List<LatexToText> latexToTextList = JSONArray.parseArray(s, LatexToText.class);
                for (LatexToText latexToText : latexToTextList) {
                    if (latexToTextMap.get(latexToText.getLatex()) != null) {
                        continue;
//                        log.info("--{}--,{}",latexToText.getLatex()+"="+latexToTextMap.get(latexToText.getLatex()),latexToText.getLatex()+"="+latexToText.getText());
                    }
                    latexToTextMap.put(latexToText.getLatex(), latexToText.getText());

                }
                log.info("latex2text.json cache success! Size:{}", latexToTextMap.size());
                break;
            }
        } catch (Exception e) {
            log.error("latex2text.json cache failed!,{}", e.toString());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    private FileReaderUtil() {
    }

    public static IResourceHolder of(String pattern) {
        if (StringUtils.equals(pattern, Constant.PRIMARY_PATTERN)) {
            return FileReaderUtilPrimaryMath.of();
        } else if (StringUtils.equals(pattern, Constant.JUNIOR_PATTERN)) {
            return FileReaderUtilJuniorMath.of();
        } else if (StringUtils.equals(pattern, Constant.JUNIOR_PHYSICS_PATTERN)) {
            return FileReaderUtilJuniorPhsics.of();
        } else if (StringUtils.equals(pattern, Constant.JUNIOR_CHEMISTRY_PATTERN)) {
            return FileReaderUtilJuniorChemistry.of();
        } else if (StringUtils.equals(pattern, Constant.HIGH_PHYSICS_PATTERN)) {
            return FileReaderUtilHighPhsics.of();
        } else if (StringUtils.equals(pattern, Constant.HIGH_MATH_PATTERN)) {
            return FileReaderUtilHighMath.of();
        } else if (StringUtils.equals(pattern, Constant.HIGH_CHEMISTRY_PATTERN)) {
            return FileReaderUtilHighChemistry.of();
        } else if (StringUtils.equals(pattern, Constant.HIGH_BIOLOGY_PATTERN)) {
            return FileReaderUtilHighBiology.of();
        } else if (StringUtils.equals(pattern, Constant.JUNIOR_BIOLOGY_PATTERN)) {
            return FileReaderUtilJuniorBiology.of();
        } else if (StringUtils.equals(pattern, Constant.JUNIOR_SCIENCE_PATTERN)) {
            return FileReaderUtilJuniorScience.of();
        } else if (StringUtils.equals(pattern, Constant.PRIMARY_SCIENCE_PATTERN)) {
            return FileReaderUtilPrimaryScience.of();
        } else if (StringUtils.equals(pattern, "")) {
            return instance;
        } else {
            log.error("传入学科学段:{},非小学数学或初中数学或初中物理，获取不到配置资源", pattern);
            return null;
        }
    }

    public static URL getFilePathURL(String fileName) throws URISyntaxException {
        URL resource = Resources.getResource(fileName);
        log.info("获取文件{}路径:{}", fileName, resource.getPath());
        return resource;
    }

    public static List<String> FileReaderAbsolutePath(String path) throws IOException {
        File f = new File(path);
        List<String> res = new ArrayList<>();
        FileInputStream fis = new FileInputStream(f);
        InputStreamReader isr = new InputStreamReader(fis, StandardCharsets.UTF_8); //以gbk编码打开文本文件
        BufferedReader br = new BufferedReader(isr, 8192 * 8);
        String line;
        while ((line = br.readLine()) != null) {
            res.add("[" + line + "]");
        }
        br.close();
        return res;
    }


    public static List<BookAnchorMap> supportAnchors(String resurcePattern) throws Exception {
        FileReaderUtil f = new FileReaderUtil();
        List<BookAnchorMap> bookAnchorMap = new ArrayList<>();
        if (resurcePattern != null) {
//        if (Constant.PRIMARY_PATTERN.equals(resurcePattern)) {
//            for (String s : f.FileReader("testfile/bookanchormap.json")) {
//                bookAnchorMap = JSONArray.parseArray(s, BookAnchorMap.class);
//            }
//        } else if (Constant.JUNIOR_PATTERN.equals(resurcePattern)) {
//            for (String s : f.FileReader("testfile/junior.json")) {
//                bookAnchorMap = JSONArray.parseArray(s, BookAnchorMap.class);
//            }
            List<String> support = new ArrayList<>(Objects.requireNonNull(FileReaderUtil.of(resurcePattern).getAnchorIdRepeat().keySet()));
            for (String s : FileReaderUtil.of(resurcePattern).getBookVersionMap().keySet()) {
                BookAnchorMap bookAnchorMap1 = new BookAnchorMap();
                bookAnchorMap1.setBookV(s);
                bookAnchorMap1.setSupporanhcors(support);
                bookAnchorMap.add(bookAnchorMap1);
            }
        } else {
            log.error("获取不到支持锚点列表!");
        }
        return bookAnchorMap;
    }

    public static List<AnchorPredictParams> consistenceTestAll() throws Exception {
        FileReaderUtil f = new FileReaderUtil();
        List<AnchorPredictParams> res = new ArrayList<>();
        for (String s : f.FileReader("utilsdata/testfile/input_primary_senior.json")) {
            List<AnchorPredictParams> anchorInfoList = JSONArray.parseArray("[" + s + "]", AnchorPredictParams.class);
            res.addAll(anchorInfoList);
        }
        return res;
    }

    public static List<AnchorPredictParams> consistenceJuniorTestAll() throws Exception {
        FileReaderUtil f = new FileReaderUtil();
        List<AnchorPredictParams> res = new ArrayList<>();
        for (String s : f.FileReader("utilsdata/testfile/input_senior.json")) {
            List<AnchorPredictParams> anchorInfoList = JSONArray.parseArray("[" + s + "]", AnchorPredictParams.class);
            res.addAll(anchorInfoList);
        }
        return res;
    }

    public static List<AnchorPredictParams> consistenceJuniorPhsicsTestAll() throws Exception {
        FileReaderUtil f = new FileReaderUtil();
        List<AnchorPredictParams> res = new ArrayList<>();
        for (String s : f.FileReader("utilsdata/testfile/phsicstest.json")) {
            List<AnchorPredictParams> anchorInfoList = JSONArray.parseArray("[" + s + "]", AnchorPredictParams.class);
            res.addAll(anchorInfoList);
        }
        return res;
    }

    public static List<AnchorPredictParams> consistenceConsole(String fineName) throws IOException {
        List<AnchorPredictParams> res = new ArrayList<>();
        String path = System.getProperty("user.dir") + "/" + fineName;
        log.info("入参文件路径:{}", path);
        for (String s : FileReaderAbsolutePath(path)) {
            List<AnchorPredictParams> anchorInfoList = JSONArray.parseArray(s, AnchorPredictParams.class);
            res.addAll(anchorInfoList);
        }
        return res;
    }

    public static List<AnchorPredictParams> consistenceTestSigle() throws Exception {
        FileReaderUtil f = new FileReaderUtil();
        List<AnchorPredictParams> res = new ArrayList<>();
        for (String s : f.FileReader("utilsdata/testfile/singleTest.json")) {
            List<AnchorPredictParams> anchorInfoList = JSONArray.parseArray("[" + s + "]", AnchorPredictParams.class);
            res.addAll(anchorInfoList);
        }
        return res;
    }

    @Override
    public Map<String, List<String>> getAnchorIdRepeat() {
        return null;
    }

    @Override
    public Map<String, String> getBookVersionMap() {
        return null;
    }

    @Override
    public Map<String, String> getAnchorIdToChapter() {
        return null;
    }

    @Override
    public Map<String, String> getAnchorIdToBookversion() {
        return null;
    }

    @Override
    public Map<String, String> getAnchorIdToSemester() {
        return null;
    }

    @Override
    public Map<String, String> getAnchorIdToName() {
        return null;
    }

    @Override
    public Map<Integer, HashSet<String>> getAnchorIdToIndexs() {
        return null;
    }

    @Override
    public Map<String, Integer> getWordToIndexs() {
        return null;
    }

    @Override
    public Map<Integer, HashSet<String>> getIndexToAnchorIdRepeat() {
        return null;
    }

    static class InstanceHolder {
        static IResourceHolder instance = new FileReaderUtil();
    }


}
