package com.iflytek.rec.anchorpredic.impl.lhsvote;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.filter.SimplePropertyPreFilter;
import com.iflytek.rec.anchorpredic.exception.EngineServiceException;
import com.iflytek.rec.anchorpredic.interfaces.lhsvote.IPhotoPortraitService;
import com.iflytek.rec.anchorpredic.pojo.ModelParams;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.*;
import com.iflytek.rec.anchorpredic.utils.lhsvote.FileReaderUtilLhs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PhotoPortraitServiceImpl implements IPhotoPortraitService {
    /**
     * 日志
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(PhotoPortraitServiceImpl.class);
    /**
     * 拍搜画像接口参数校验
     */
    private static final PhotoPortraitValidatorImpl photoPortraitValidator = new PhotoPortraitValidatorImpl();

    private static final PhotoPortraitBusinessImpl photoPortraitBusiness = new PhotoPortraitBusinessImpl();
    private static final String dataApiPath = "/native/cfg/dataApiConfig.properties";

    @Override
    public void initModel(ModelParams modelParams) {
        String dataApiConfig = modelParams.getWorkPath() + dataApiPath;
        try {
            FileReaderUtilLhs.DataApiConfigReader(dataApiConfig);
            LOGGER.info("【LHS锚点预测模型加载成功】dataApiConfig文件路径:{}", dataApiConfig);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("【LHS锚点预测模型加载错误】dataApiConfig文件路径错误:{}", dataApiConfig);
        }
    }

    @Override
    public PointRecgResponse pointRecg(PointRecgRequest request) throws EngineServiceException {
        //打印接口入参
        long startSerial = System.currentTimeMillis();
        if (LOGGER.isInfoEnabled()) {
            //序列化输出时不输出ocr文本
            SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
            filter.getExcludes().add("ocrText");
            LOGGER.info("拍搜画像-点识别请求:{}", JSON.toJSONString(request, filter));
        }
        LOGGER.info("拍搜画像-点识别接口:pointRecg,入参打印耗时:{}ms", System.currentTimeMillis() - startSerial);

        //接口参数校验
        long startValidation = System.currentTimeMillis();
        photoPortraitValidator.pointRecgValidation(request);
        LOGGER.info("拍搜画像-点识别参数校验完成，耗时:{}ms", System.currentTimeMillis() - startValidation);

        PointRecgResponse response = photoPortraitBusiness.pointRecg(request);
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("拍搜画像-点识别接口:pointRecg,接口返回结果:{}", JSON.toJSONString(response));
        }
        //保存点识别日志到日志系统（es）
//        savePointRecgLogToLogSystem(request, response);

        LOGGER.info("拍搜画像-点识别接口:pointRecg,接口调用总耗时:{}ms", System.currentTimeMillis() - startSerial);

        return response;
    }
}
