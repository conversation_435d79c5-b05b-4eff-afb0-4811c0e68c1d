package com.iflytek.rec.anchorpredic.impl.lhsvote;

import com.alibaba.fastjson2.JSON;
import com.iflytek.rec.anchorpredic.dataapi.AiDiagnosisDataProxy;
import com.iflytek.rec.anchorpredic.interfaces.lhsvote.AnchorPredictBusiness;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.*;
import com.iflytek.rec.anchorpredic.utils.lhsvote.FileReaderUtilLhs;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

public class AnchorPredictBusinessImpl implements AnchorPredictBusiness {
    /**
     * logger
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(AnchorPredictBusinessImpl.class);
    /**
     * 锚点预测返回top10结果
     */
    private static final int TOPK = 10;
    /**
     * 调用锚点预测引擎需要传入的用户名称、用户密码
     */
    private static final String USER = "admin";
    /**
     * 置信度阈值
     * 置信度大于等于0.6：题库试题信息预测锚点。小于0.6：OCR题面预测锚点
     */
    private static final Double THR_CFDS = 0.6;
    /**
     * 置信度阈值：0.8，用于判断是否参与投票
     */
    private static final Double HIGH_THR_CFDS = 0.8;
    /**
     * 无效的试题来源集合
     */
    private static final List<String> INVALID_SOURCE_TYPE = Arrays.asList("13", "12", "09", "10", "");
    /**
     * 拍搜预测试题最大查询量 MongoDB
     */
    private static final Integer MAX_TOPIC_LIST = 400;

    private static final Integer MAX_HIGH_SHCOOL_CHEMISTRY_TOPIC = 10;

    private static final Integer MAX_PRIMARY_SHCOOL_TPOIC = 5;


    @Override
    public List<PhotoTopicOutput> offlinePredict(PointRecgRequest request) {
        //key：试题标识id    value：拍搜预测试题id列表
        Map<String, List<String>> topicMap = new HashMap<>();
        for (PhotoTopicInput input : request.getPhotoTopicInputList()) {
            List<String> topicIds = new ArrayList<>();
            for (TopicInfo info : input.getTopicInfos()) {
                topicIds.add(info.getTopicId());
            }
            topicMap.put(input.getId(), topicIds);
        }

        //本次所有预测试题id列表
        List<String> topicIdList = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : topicMap.entrySet()) {
            topicIdList.addAll(entry.getValue());
        }

        //获取教材版本下试题锚点预测结果   key：预测试题id    value：预测锚点id
        Map<String, TopicAnchorInfo> topicAnchorMap = anchorPredDataApiFirst(topicIdList, request.getBookVersionCode());
        //重新构造Request,只为代码复用
        MiddleRequest middleRequest = new MiddleRequest();
        BeanUtils.copyProperties(request, middleRequest);
        //进行新锚点映射，防止推出无效锚点(拍搜画像-点识别)
        //修改逻辑：从数据库查询到入参试题指定的教材版本下锚点预测结果后，将锚点进行新-旧锚点映射
        //影响范围：拍搜画像-点识别接口PhotoPortraitService.pointRecg
        //涉及缓存表：TopicInfo(拍搜画像试题信息表,存储试题对应的锚点置信度)
        //映射失败造成问题：旧锚点可能无法查找到该版本下对应的章节信息，会被直接丢弃，现象为丢失部分原题的锚点识别结果
        newOldAnchorMapping(middleRequest, topicAnchorMap);

        List<TopicAnchorInfo> anchorInfoList = new ArrayList<>();
        anchorInfoList = anchorPointPredictByVote(request, topicAnchorMap);

        //构建预测结果
        List<PhotoTopicOutput> outputList = buildOutPut(middleRequest, anchorInfoList);

        //预测出锚点集合、章节集合
        Set<String> anchorSet = new HashSet<>();
        Set<String> catalogSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(outputList)) {
            for (PhotoTopicOutput output : outputList) {
                anchorSet.add(output.getPointId());
                catalogSet.add(output.getCatalogCode());
            }
        }
        LOGGER.info("预测不重复锚点个数:{},锚点列表:{},不重复章节个数:{},章节列表:{}", anchorSet.size(), anchorSet,
                catalogSet.size(), catalogSet);

        return outputList;
    }

    private List<TopicAnchorInfo> anchorPointPredictByVote(PointRecgRequest request, Map<String, TopicAnchorInfo> topicAnchorMap) {
        List<TopicAnchorInfo> anchorInfoList = new ArrayList<>();
        try {
            int count = 0;
            for (PhotoTopicInput input : request.getPhotoTopicInputList()) {
                //该道原题对应候选预测锚点信息
                List<TopicAnchorInfo> infoList = new ArrayList<>();
                for (TopicInfo info : input.getTopicInfos()) {
                    if (topicAnchorMap.containsKey(info.getTopicId())) {
                        //试题id、锚点id、置信度
                        TopicAnchorInfo anchorInfo = new TopicAnchorInfo();
                        BeanUtils.copyProperties(topicAnchorMap.get(info.getTopicId()), anchorInfo);
                        anchorInfo.setTopicId(info.getTopicId());
                        anchorInfo.setId(input.getId());
                        anchorInfo.setSearchCfdsLevel(info.getCfdsLevel());
                        infoList.add(anchorInfo);
                    }
                }
                count += 1;
                sortSearchDesc(infoList);
                //小学投票逻辑
                if (StringUtils.equals(request.getPhaseCode(), "03")) {
                    predictPrimarySchool(infoList, anchorInfoList);
                    if (anchorInfoList.isEmpty()) {
                        LOGGER.warn("第{}题,试题标识id:{} ,该道原题对应候选预测锚点信息为空！！！小学投票逻辑投票结束！！！", count, input.getId());
                    } else {
                        LOGGER.info("第{}题,试题标识id:{} ,进入小学投票逻辑,投票结果:{}", count, input.getId(), anchorInfoList.subList(0, count).toString());
                    }
                }
                //主流程投票逻辑,非小学 走该投票逻辑
                else {
                    predictMain(infoList, anchorInfoList);
                    if (anchorInfoList.isEmpty()) {
                        LOGGER.warn("第{}题,试题标识id:{} ,该道原题对应候选预测锚点信息为空！！！主流程投票结束！！！", count, input.getId());
                    } else {
                        LOGGER.info("第{}题,试题标识id:{} ,主流程投票逻辑,投票结果:{}", count, input.getId(), anchorInfoList.subList(0, count).toString());
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("获取题库试题锚点预测结果失败_" + e.getMessage(), e);
            anchorInfoList.clear();
            batchVote(request, topicAnchorMap, anchorInfoList);
            LOGGER.info("投票兜底,主流程投票逻辑,投票结果:{}", anchorInfoList.toString());
        }
        return anchorInfoList;
    }

    private void predictMain(List<TopicAnchorInfo> infoList, List<TopicAnchorInfo> topicAnchorInfos) {

        int infoListSize = infoList.size();
        if (infoListSize == 0) {
            LOGGER.warn("该道原题对应候选预测锚点信息为空!");
            return;
        }
        TopicAnchorInfo topicAnchorInfoOne = infoList.get(0);  //第一题
        //候选试题数目只有1题 返回唯一试题对应的锚点id
        if (infoListSize < 2) {
            LOGGER.info("候选试题数目只有1题 返回唯一试题对应的锚点id");
            topicAnchorInfos.add(topicAnchorInfoOne);
            return;
        }
        TopicAnchorInfo topicAnchorInfoTwo = infoList.get(1); //第二题
        //top1搜题置信度>0.9 或 top1搜题置信度大于0.75  且 top1-top2大于0.3
        topicAnchorInfos.add(topicAnchorInfoOne.getSearchCfdsLevel() > 0.9 ? predictMainMethodOne((infoList.subList(0, Math.min(infoListSize, MAX_HIGH_SHCOOL_CHEMISTRY_TOPIC)))) : (topicAnchorInfoOne.getSearchCfdsLevel() > 0.75) && ((topicAnchorInfoOne.getSearchCfdsLevel() - topicAnchorInfoTwo.getSearchCfdsLevel()) > 0.3) ? topicAnchorInfoOne : predictMainMethodTwo(infoList.subList(0, Math.min(infoListSize, MAX_HIGH_SHCOOL_CHEMISTRY_TOPIC))));
    }

    private TopicAnchorInfo predictMainMethodOne(List<TopicAnchorInfo> infoList) {
        float count = 0f;
        List<TopicAnchorInfo> highSearchCfdsLevelList = new ArrayList<>();
        for (TopicAnchorInfo info : infoList) {
            if (info.getSearchCfdsLevel() >= 0.8) {
                count += 1;
                highSearchCfdsLevelList.add(info);
            }
        }
        //搜题置信度大于0.8占比70%
        boolean isTrue = count / infoList.size() > 0.7f;
        if (isTrue) {
            LOGGER.info("搜题置信度大于0.8占比超70%,取highSearchCfdsLevelList-top1");
            TopicAnchorInfo highSearchCfdsTopic = highSearchCfdsLevelList.get(0);
            highSearchCfdsLevelList.removeIf(l -> INVALID_SOURCE_TYPE.contains(l.getSource()));
            return highSearchCfdsLevelList.size() != 0 ? highSearchCfdsLevelList.get(0) : highSearchCfdsTopic;
        } else {
            LOGGER.info("搜题置信度大于0.8占比不超70%,取top1");
            return infoList.get(0);
        }
    }

    private TopicAnchorInfo predictMainMethodTwo(List<TopicAnchorInfo> infoList) {
        Map<String, List<TopicAnchorInfo>> anchorInfoMap = new HashMap<>();
        for (TopicAnchorInfo info : infoList) {
            if (!anchorInfoMap.containsKey(info.getAnchorPointId())) {
                anchorInfoMap.put(info.getAnchorPointId(), new ArrayList<TopicAnchorInfo>());
            }
            anchorInfoMap.get(info.getAnchorPointId()).add(info);
        }
        //key：预测重复次数  value：预测重复次数等于key的锚点id列表
        Map<Integer, List<String>> numAnchorMap = new HashMap<>();
        for (Map.Entry<String, List<TopicAnchorInfo>> entry : anchorInfoMap.entrySet()) {
            Integer size = entry.getValue().size();
            if (!numAnchorMap.containsKey(size)) {
                numAnchorMap.put(size, new ArrayList<String>());
            }
            numAnchorMap.get(size).add(entry.getKey());
        }

        //获取次数最大的锚点列表
        Integer maxSize = Collections.max(numAnchorMap.keySet());
        //获取最大的锚点id列表
        List<String> maxAnchorList = numAnchorMap.get(maxSize);
        //top10对应的锚点各不相同 或者top1搜题置信度小于0.55 返回top1试题对应的锚点id
        if (maxSize == 1 || infoList.get(0).getSearchCfdsLevel() < 0.55) {
            LOGGER.info("top10对应的锚点各不相同 返回top1试题对应的锚点id");
            return infoList.get(0);
        } else {
            if (maxAnchorList.size() == 1) {
                //top1-票数最多的锚点>0.35 返回top1
                if (infoList.get(0).getSearchCfdsLevel() - anchorInfoMap.get(maxAnchorList.get(0)).get(0).getSearchCfdsLevel() > 0.35) {
                    LOGGER.info("top1-票数最多的锚点>0.35,返回top1");
                    return infoList.get(0);
                }
                //返回票数最多的锚点
                LOGGER.info("返回票数最多的锚点");
                return anchorInfoMap.get(maxAnchorList.get(0)).get(0);
            } else {
                TopicAnchorInfo topicAnchorInfo = anchorInfoMap.get(maxAnchorList.get(0)).get(0);
                for (String anchor : maxAnchorList) {
                    //存在相同票数的锚点 返回搜题置信度最高的锚点
                    if (anchorInfoMap.get(anchor).get(0).getSearchCfdsLevel() > topicAnchorInfo.getSearchCfdsLevel()) {
                        topicAnchorInfo = anchorInfoMap.get(anchor).get(0);
                    }
                    ////存在相同票数的锚点 且搜题置信度一样 返回题目位置最靠前的锚点
                    else if (Double.doubleToLongBits(anchorInfoMap.get(anchor).get(0).getSearchCfdsLevel()) == Double.doubleToLongBits(topicAnchorInfo.getSearchCfdsLevel()) && (infoList.indexOf(anchorInfoMap.get(anchor).get(0)) < infoList.indexOf(topicAnchorInfo))) {
                        topicAnchorInfo = anchorInfoMap.get(anchor).get(0);
                    }
                }
                if (infoList.get(0).getSearchCfdsLevel() - topicAnchorInfo.getSearchCfdsLevel() > 0.35) {
                    LOGGER.info("存在相同票数的锚点,top1-搜题置信度最高的锚点>0.35,返回top1");
                    return infoList.get(0);
                }
                LOGGER.info("存在相同票数的锚点 返回搜题置信度最高的锚点");
                return topicAnchorInfo;
            }
        }
    }

    private void predictPrimarySchool(List<TopicAnchorInfo> infoList, List<TopicAnchorInfo> topicAnchorInfos) {
        if (infoList.isEmpty()) {
            LOGGER.warn("该道原题对应候选预测锚点信息为空!");
            return;
        }
        TopicAnchorInfo topicAnchorInfo = infoList.get(0);
        int infoListSize = infoList.size();
        //候选试题数目只有1题 返回唯一试题对应的锚点id
        if (infoListSize < 2) {
            topicAnchorInfos.add(topicAnchorInfo);
            return;
        }
        //top1搜题置信度>0.9 返回top1试题对应的锚点id
        if (topicAnchorInfo.getSearchCfdsLevel() > 0.9) {
            topicAnchorInfos.add(topicAnchorInfo);
            return;
        }
        TopicAnchorInfo topicAnchorInfo1 = infoList.get(1);
        //top1搜题置信度>0.7 且top1-top2>0.38 返回top1试题对应的锚点id
        if ((topicAnchorInfo.getSearchCfdsLevel() > 0.7) && ((topicAnchorInfo.getSearchCfdsLevel() - topicAnchorInfo1.getSearchCfdsLevel()) > 0.38)) {
            topicAnchorInfos.add(topicAnchorInfo);
        } else {
            //top5试题投票 不够5题 topN试题投票
            topicAnchorInfos.add(primarySchoolVote(infoList.subList(0, Math.min(infoListSize, MAX_PRIMARY_SHCOOL_TPOIC))));
        }
    }

    private TopicAnchorInfo primarySchoolVote(List<TopicAnchorInfo> infoList) {
        String pointId;
        Map<String, List<TopicAnchorInfo>> anchorInfoMap = new HashMap<>();
        for (TopicAnchorInfo info : infoList) {
            if (!anchorInfoMap.containsKey(info.getAnchorPointId())) {
                anchorInfoMap.put(info.getAnchorPointId(), new ArrayList<TopicAnchorInfo>());
            }
            anchorInfoMap.get(info.getAnchorPointId()).add(info);
        }
        //key：预测重复次数  value：预测重复次数等于key的锚点id列表
        Map<Integer, List<String>> numAnchorMap = new HashMap<>();
        for (Map.Entry<String, List<TopicAnchorInfo>> entry : anchorInfoMap.entrySet()) {
            Integer size = entry.getValue().size();
            if (!numAnchorMap.containsKey(size)) {
                numAnchorMap.put(size, new ArrayList<String>());
            }
            numAnchorMap.get(size).add(entry.getKey());
        }

        //获取次数最大的锚点列表
        Integer maxSize = Collections.max(numAnchorMap.keySet());
        //获取最大的锚点id列表
        List<String> maxAnchorList = numAnchorMap.get(maxSize);
        //top5对应的锚点各不相同 返回top1试题对应的锚点id
        if (maxSize == 1) {
            pointId = infoList.get(0).getAnchorPointId();
        } else {
            //票数最多锚点仅有一个 锚点id确定为该结果
            if (maxAnchorList.size() == 1) {
                pointId = maxAnchorList.get(0);
            } else {
                //搜题位置index加和小的同票锚点优先
                Map<Integer, String> indexAnchorMap = new HashMap<>();
                for (String anchor : maxAnchorList) {
                    int ind = 0;
                    int count = 0;
                    for (TopicAnchorInfo info : infoList) {
                        if (anchor.equals(info.getAnchorPointId())) {
                            count += ind;
                        }
                        ind += 1;
                    }
                    indexAnchorMap.put(count, anchor);
                }
                pointId = indexAnchorMap.get(Collections.min(indexAnchorMap.keySet()));
            }
        }
        //锚点对应锚点信息  key：锚点id    value：锚点信息（保留置信度较高锚点）
        Map<String, TopicAnchorInfo> highCfdsAnchorMap = new HashMap<>();
        for (TopicAnchorInfo info : infoList) {
            String id = info.getAnchorPointId();
            if (highCfdsAnchorMap.containsKey(id) && (info.getSearchCfdsLevel() <= highCfdsAnchorMap.get(id).getSearchCfdsLevel())) {
                continue;
            }
            highCfdsAnchorMap.put(id, info);

        }
        TopicAnchorInfo anchorInfo = new TopicAnchorInfo();
        BeanUtils.copyProperties(highCfdsAnchorMap.get(pointId), anchorInfo);
        return anchorInfo;
    }


    private void newOldAnchorMapping(MiddleRequest request, Map<String, TopicAnchorInfo> topicAnchorMap) {
        //进行新锚点映射，防止推出无效锚点(拍搜画像-点识别)
        //修改逻辑：从数据库查询到入参试题指定的教材版本下锚点预测结果后，将锚点进行新-旧锚点映射
        //影响范围：拍搜画像-点识别接口PhotoPortraitService.pointRecg
        //涉及缓存表：TopicInfo(拍搜画像试题信息表,存储试题对应的锚点置信度)
        //映射失败造成问题：旧锚点可能无法查找到该版本下对应的章节信息，会被直接丢弃，现象为丢失部分原题的锚点识别结果
        if (CollectionUtils.isNotEmpty(topicAnchorMap.keySet())) {
            Map<String, String> old2newMap = new HashMap<>();
            for (String topicId : topicAnchorMap.keySet()) {
                TopicAnchorInfo topicAnchorInfo = topicAnchorMap.get(topicId);
                if (null == topicAnchorInfo) {
                    continue;
                }
                String oldPointId = topicAnchorInfo.getAnchorPointId();
                String newPointId = oldPointId;
                List<NewAnchorInfo> newAnchorInfos = FileReaderUtilLhs.of().getOanchorIdToNanchorId().getOrDefault(oldPointId, new ArrayList<>()).stream().filter(item -> StringUtils.equals(request.getBookVersionCode(), item.getBookVersionCode())).collect(Collectors.toList());
                NewAnchorInfo newAnchorInfo = newAnchorInfos.stream().sorted(Comparator.comparing(NewAnchorInfo::getChapterCodeLength)).sorted(Comparator.comparing(NewAnchorInfo::getChapterCode)).findAny().orElse(new NewAnchorInfo());
                if (!newAnchorInfos.isEmpty()) {
                    newPointId = newAnchorInfo.getAnchorId();
                }
                old2newMap.put(oldPointId, newPointId);
                topicAnchorInfo.setAnchorPointId(newPointId);
            }
            LOGGER.info("【拍搜画像-点识别】subjectCode:{},phaseCode:{},bookCode:{},old2newMap:{}", request.getSubjectCode(), request.getPhaseCode(), request.getBookVersionCode(), JSON.toJSONString(old2newMap));
        }
    }


    private void batchVote(PointRecgRequest request, Map<String, TopicAnchorInfo> topicAnchorMap, List<TopicAnchorInfo> anchorInfoList) {
        //投票策略获取每道试题最终预测锚点
        for (PhotoTopicInput input : request.getPhotoTopicInputList()) {
            //该道原题对应候选预测锚点信息
            List<TopicAnchorInfo> infoList = new ArrayList<>();
            for (TopicInfo info : input.getTopicInfos()) {
                if (topicAnchorMap.containsKey(info.getTopicId())) {
                    //试题id、锚点id、置信度
                    TopicAnchorInfo anchorInfo = new TopicAnchorInfo();
                    BeanUtils.copyProperties(topicAnchorMap.get(info.getTopicId()), anchorInfo);
                    anchorInfo.setTopicId(info.getTopicId());
                    anchorInfo.setId(input.getId());

                    infoList.add(anchorInfo);
                }
            }
            //投票结果，并添加原题标识id
            LOGGER.info("试题标识id:{},开始进行预测锚点投票", input.getId());
            TopicAnchorInfo anchorInfo = vote(infoList);

            //获取到投票结果
            if (null != anchorInfo) {
                anchorInfo.setId(input.getId());
                anchorInfoList.add(anchorInfo);
            }
        }
    }

    /**
     * 根据试题id、教材版本获取预测锚点信息 优先使用dataApi
     *
     * @param topicIdList 试题id列表
     * @param versionCode 教材版本code
     * @return 教材版本下试题锚点预测结果   key：试题id    value：预测锚点id
     */
    private Map<String, TopicAnchorInfo> anchorPredDataApiFirst(List<String> topicIdList, String versionCode) {

        //获取教材版本下试题锚点预测结果   key：预测试题id    value：预测锚点id
        Map<String, TopicAnchorInfo> topicAnchorMap = new HashMap<>();
        if (CollectionUtils.isEmpty(topicIdList)) {
            return topicAnchorMap;
        }

        //dataApi
        predByDataApi(topicIdList, versionCode, topicAnchorMap);


        return topicAnchorMap;
    }


    /**
     * dataApi实时查询预测
     *
     * @param topicIdList
     * @param versionCode
     * @param topicAnchorMap
     */
    private void predByDataApi(List<String> topicIdList, String versionCode, Map<String, TopicAnchorInfo> topicAnchorMap) {
//         调用dataAPI获取数据
        String resStr = AiDiagnosisDataProxy.getAiDiagnosisDataByDataApi(topicIdList, versionCode);
        AiDiagnosisDataProxy.processTopicAnchorMapByResStr(resStr, topicAnchorMap);
    }

    /**
     * 输入一批预测锚点id+置信度，从中投票出最终预测锚点id
     *
     * @param infoList 预测锚点列表
     * @return 预测锚点信息（入参为空，返回null）
     * 1、筛选预测置信度大于等于0.8的预测结果进行投票，选择票数最多的锚点作为最终预测锚点，
     * 若有平票情况，则选择平票锚点中平均预测置信度更高的锚点。
     * 2、若搜题结果中没有试题满足筛选条件（大于等于0.8），则直接选择所有预测结果中置信度最高的锚点。
     */
    private TopicAnchorInfo vote(List<TopicAnchorInfo> infoList) {
        //默认值：空字符串，未预测出来的情况
        String pointId = StringJoinChar.EMPTY_CHAR;
        if (CollectionUtils.isEmpty(infoList)) {
            return null;
        }

        //高置信度锚点预测结果(置信度大于0.8)
        //各个锚点map   key：锚点id    value：锚点信息列表
        Map<String, List<TopicAnchorInfo>> anchorInfoMap = new HashMap<>();
        for (TopicAnchorInfo info : infoList) {
            //有效试题来源且大于0.8
            if (validSource(info.getSource()) && (info.getCfdsLevel() >= HIGH_THR_CFDS)) {
                if (!anchorInfoMap.containsKey(info.getAnchorPointId())) {
                    anchorInfoMap.put(info.getAnchorPointId(), new ArrayList<TopicAnchorInfo>());
                }
                anchorInfoMap.get(info.getAnchorPointId()).add(info);
            }
        }

        //存在置信度大于0.8的预测锚点
        if (CollectionUtils.isNotEmpty(anchorInfoMap.keySet())) {
            //key：预测重复次数  value：预测重复次数等于key的锚点id列表
            Map<Integer, List<String>> numAnchorMap = new HashMap<>();
            for (Map.Entry<String, List<TopicAnchorInfo>> entry : anchorInfoMap.entrySet()) {
                Integer size = entry.getValue().size();
                if (!numAnchorMap.containsKey(size)) {
                    numAnchorMap.put(size, new ArrayList<String>());
                }
                numAnchorMap.get(size).add(entry.getKey());
            }

            //获取次数最大的锚点列表
            Integer maxSize = Collections.max(numAnchorMap.keySet());
            //获取最大的锚点id列表
            List<String> maxAnchorList = numAnchorMap.get(maxSize);
            //票数最多锚点仅有一个，锚点id确定为该结果。否则选取平均掌握度最高锚点
            if (maxAnchorList.size() == 1) {
                pointId = maxAnchorList.get(0);
            } else {
                //每个锚点平均掌握度
                List<TopicAnchorInfo> anchorInfos = new ArrayList<>();
                for (String id : maxAnchorList) {
                    //锚点平均掌握度
                    Double cfdsAvg = calCfdsAvg(anchorInfoMap.get(id));
                    TopicAnchorInfo anchorInfo = new TopicAnchorInfo();
                    anchorInfo.setAnchorPointId(id);
                    anchorInfo.setCfdsLevel(cfdsAvg);

                    anchorInfos.add(anchorInfo);
                }
                //降序排序
                sortDesc(anchorInfos);
                //取置信度最高锚点id
                pointId = anchorInfos.get(0).getAnchorPointId();
            }
            LOGGER.info("锚点投票,最多票数:{},投票最多锚点id列表:{},选定锚点id:{}", maxSize, maxAnchorList, pointId);
        }

        //不满足投票条件
        if (StringUtils.isEmpty(pointId)) {
            //所有锚点信息，按置信度排序获取
            sortDesc(infoList);
            pointId = infoList.get(0).getAnchorPointId();
            LOGGER.info("置信度排序,最高置信度锚点id:{}", pointId);
        }

        //锚点对应锚点信息  key：锚点id    value：锚点信息（保留置信度较高锚点）
        Map<String, TopicAnchorInfo> highCfdsAnchorMap = new HashMap<>();
        for (TopicAnchorInfo info : infoList) {
            String id = info.getAnchorPointId();
            if (highCfdsAnchorMap.containsKey(id) && (info.getCfdsLevel() <= highCfdsAnchorMap.get(id).getCfdsLevel())) {
                continue;
            }
            highCfdsAnchorMap.put(id, info);

        }
        TopicAnchorInfo anchorInfo = new TopicAnchorInfo();
        BeanUtils.copyProperties(highCfdsAnchorMap.get(pointId), anchorInfo);

        return anchorInfo;
    }

    /**
     * 判断是否是有效来源试题
     *
     * @param source 试题来源
     * @return true：有效试题来源   false：无效试题来源
     */
    private boolean validSource(String source) {
        if (StringUtils.isEmpty(source)) {
            return false;
        }

        return !INVALID_SOURCE_TYPE.contains(source);
    }

    /**
     * 计算置信度平均值
     *
     * @param infoList 锚点信息
     * @return 置信度平均值（入参为空返回默认值0）
     */
    private Double calCfdsAvg(List<TopicAnchorInfo> infoList) {
        //默认值为0，入参为空返回默认值
        Double cfdsAvg = 0.0;
        if (CollectionUtils.isEmpty(infoList)) {
            return cfdsAvg;
        }

        //求和
        for (TopicAnchorInfo info : infoList) {
            cfdsAvg += info.getCfdsLevel();
        }

        //平均值
        cfdsAvg = cfdsAvg / infoList.size();

        return cfdsAvg;
    }

    /**
     * 入参锚点信息按照置信度降序排序
     *
     * @param anchorInfoList 锚点信息
     */
    private void sortDesc(List<TopicAnchorInfo> anchorInfoList) {
        Collections.sort(anchorInfoList, new Comparator<TopicAnchorInfo>() {
            @Override
            public int compare(TopicAnchorInfo o1, TopicAnchorInfo o2) {
                // cfdsLevel大的提前，根据cfdsLevel降序排列
                return o2.getCfdsLevel().compareTo(o1.getCfdsLevel());
            }
        });
    }

    /**
     * 入参锚点信息按照搜题置信度降序排序
     *
     * @param anchorInfoList 锚点信息
     */
    private void sortSearchDesc(List<TopicAnchorInfo> anchorInfoList) {
        Collections.sort(anchorInfoList, new Comparator<TopicAnchorInfo>() {
            @Override
            public int compare(TopicAnchorInfo o1, TopicAnchorInfo o2) {
                // cfdsLevel大的提前，根据cfdsLevel降序排列
                return o2.getSearchCfdsLevel().compareTo(o1.getSearchCfdsLevel());
            }
        });
    }


    /**
     * 根据试题锚点预测结果构建返回结果
     *
     * @param request  学科、学段、教材版本code、入参试题信息
     * @param infoList 试题id及预测锚点id
     * @return 试题点识别返回结果
     */
    private List<PhotoTopicOutput> buildOutPut(MiddleRequest request, List<TopicAnchorInfo> infoList) {

        if (CollectionUtils.isEmpty(infoList)) {
            return new ArrayList<>();
        }

        //所以试题预测结果：试题标识id-->锚点id映射关系   key：试题标识id    value：锚点id
        Map<String, TopicAnchorInfo> topicAnchorMap = new HashMap<>();
        //锚点id列表
        List<String> anchorPointList = new ArrayList<>();
        for (TopicAnchorInfo info : infoList) {
            topicAnchorMap.put(info.getId(), info);
            if (!anchorPointList.contains(info.getAnchorPointId())) {
                anchorPointList.add(info.getAnchorPointId());
            }
        }

        //获取锚点id对应的章节code
        BasicRequest basicRequest = new BasicRequest();
        BeanUtils.copyProperties(request, basicRequest);
        Map<String, String> anchorCatalogMap = FileReaderUtilLhs.of().getNanchorIdToChapter();
        //获取锚点id对应的难度
        Map<String, Integer> anchorDiffMap = FileReaderUtilLhs.of().getNanchorIdToDifficult();

        //锚点预测结果
        List<PhotoTopicOutput> outputList = new ArrayList<>();
        for (PhotoTopicInput input : request.getPhotoTopicInputList()) {
            if (!topicAnchorMap.containsKey(input.getId())) {
                continue;
            }
            //锚点id
            String anchorPointId = topicAnchorMap.get(input.getId()).getAnchorPointId();
            //存在难度、章节code
            if ((anchorCatalogMap.containsKey(anchorPointId)) && (anchorDiffMap.containsKey(anchorPointId))) {
                try {
                    String[] catalogs = anchorCatalogMap.getOrDefault(anchorPointId, "null_null").split("_");
                    if (StringUtils.equals(catalogs[0], request.getBookVersionCode())) {
                        setoutputList(topicAnchorMap, anchorCatalogMap, anchorDiffMap, input, outputList);
                    }
                }catch (Exception e){
                    LOGGER.error("章节分割失败 锚点弃用!!");
                }

            }
        }

        return outputList;
    }


    private void setoutputList
            (Map<String, TopicAnchorInfo> topicAnchorMap, Map<String, String> anchorCatalogMap, Map<String, Integer> anchorDiffMap, PhotoTopicInput
                    input, List<PhotoTopicOutput> outputList) {
        //锚点id
        String anchorPointId = topicAnchorMap.get(input.getId()).getAnchorPointId();
        PhotoTopicOutput output = new PhotoTopicOutput();
        //试题标识id
        output.setId(input.getId());
        //试题id
        output.setTopicId(topicAnchorMap.get(input.getId()).getTopicId());
        //锚点id
        output.setPointId(anchorPointId);
        //点类型：锚点
        output.setPointType(PointType.ANCHOR_POINT);
        //章节code
        output.setCatalogCode(anchorCatalogMap.get(anchorPointId));
        //试题难度，使用对应锚点难度
        output.setTopicDiff(anchorDiffMap.get(anchorPointId));

        //添加到返回结果列表中
        outputList.add(output);
    }

}
