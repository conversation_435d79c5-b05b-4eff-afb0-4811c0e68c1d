package com.iflytek.rec.anchorpredic.impl.lhsvote;

import com.iflytek.rec.anchorpredic.exception.EngineServiceException;
import com.iflytek.rec.anchorpredic.interfaces.lhsvote.IPhotoPortraitValidator;
import com.iflytek.rec.anchorpredic.method.lhsvote.ConstantValidlateUtil;
import com.iflytek.rec.anchorpredic.method.lhsvote.DigitUtil;
import com.iflytek.rec.anchorpredic.method.lhsvote.ValidationUtils;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class PhotoPortraitValidatorImpl implements IPhotoPortraitValidator {
    /**
     * 日志
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(PhotoPortraitValidatorImpl.class);

    @Override
    public void pointRecgValidation(PointRecgRequest request) throws EngineServiceException{
        //入参为null
        pointValidate(null == request, ValidationUtils.validateEntity(request), request.getPointType(), request.getPhaseCode(), request.getGradeCode(), request.getSubjectCode(), request.getBookVersionCode(), request.getPhotoTopicInputList());
    }


    /**
     * 抽取公共参数的校验
     * <AUTHOR>
     * @date 2022/5/13 16:56
     */

    private void pointValidate(boolean b, StringBuilder stringBuilder, String pointType, String phaseCode, String gradeCode, String subjectCode, String bookVersionCode, List<PhotoTopicInput> photoTopicInputList) throws EngineServiceException {
        //入参为null
        if (b) {
            throw new EngineServiceException(ResponseCode.PARAM_VALIDATION_ERROR, ValidationMessage.INPUT_NULL_ERROR);
        }

        // 调用JSR303验证工具，校验参数校验PointRecgRequest
        long startValida = System.currentTimeMillis();
        StringBuilder errMessage = stringBuilder;
        LOGGER.info("拍搜画像-点识别参数校验：调用JSR303工具进行参数校验耗时:{}ms",
                System.currentTimeMillis() - startValida);

        //注解校验存在问题
        if (errMessage.length() > 0) {
            throw new EngineServiceException(ResponseCode.PARAM_VALIDATION_ERROR, errMessage.toString());
        }

        //校验点类型是否合法
        if (!ConstantValidlateUtil.validatePhotoPointType(pointType)) {
            throw new EngineServiceException(ResponseCode.PARAM_VALIDATION_ERROR, ValidationMessage.ILEGAL_POINT_TYPE);
        }

        //学段、年级信息不匹配
        if (!GradeCode.validatePhaseGeade(phaseCode, gradeCode)) {
            throw new EngineServiceException(ResponseCode.PARAM_VALIDATION_ERROR, ValidationMessage.ILEGAL_PHASE_GRADE);
        }

        //校验是否包含教材版本
//        if (!MongoCacheManager.isContainBookVersion(subjectCode, phaseCode,
//                bookVersionCode)) {
//            throw new EngineServiceException(ResponseCode.PARAM_VALIDATION_ERROR, ValidationMessage.ILEGAL_BOOK_VERSION);
//        }

        //错误信息
        List<String> errMessageList = new ArrayList<>();
        //校验试题得分、置信度
        for (PhotoTopicInput input : photoTopicInputList) {
            //得分不满足score <= standardScore则抛异常,已传得分，进行得分合法性校验
            if ((input.getScore() > input.getStandardScore())
                    && DigitUtil.isNotEqual(input.getScore(), input.getStandardScore())) {
                //错误信息包含试题id，方便查找
                String message = input.getId() + "_得分:" + input.getScore()
                        + ",大于标准分:" + input.getStandardScore();
                errMessageList.add(message);
            }
            //置信度大于1，抛异常
            for (TopicInfo topicInfo : input.getTopicInfos()) {
                if (topicInfo.getCfdsLevel() > 1) {
                    String message = input.getId() + "_" + topicInfo.getTopicId()
                            + "_置信度:" + topicInfo.getCfdsLevel() + ",大于1";
                    errMessageList.add(message);
                }
            }
        }
        //有错误信息，则抛异常
        if (CollectionUtils.isNotEmpty(errMessageList)) {
            throw new EngineServiceException(ResponseCode.PARAM_VALIDATION_ERROR, StringUtils.join(errMessageList, ";"));
        }
    }
}
