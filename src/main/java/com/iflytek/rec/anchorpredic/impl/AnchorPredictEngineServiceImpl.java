package com.iflytek.rec.anchorpredic.impl;

import com.alibaba.fastjson2.JSON;
import com.iflytek.infer.domain.Content;
import com.iflytek.infer.domain.DataFrame;
import com.iflytek.infer.domain.InferDataTypeEnum;
import com.iflytek.infer.interfaces.params.InferParam;
import com.iflytek.infer.interfaces.params.InferResult;
import com.iflytek.rec.anchorpredic.interfaces.IAnchorPredictEngineService;
import com.iflytek.rec.anchorpredic.interfaces.IAnchorPredictMethod;
import com.iflytek.rec.anchorpredic.method.paramAnalysis.ParamAnalysisHolder;
import com.iflytek.rec.anchorpredic.model.ModelHolder;
import com.iflytek.rec.anchorpredic.model.ModelScopeEnum;
import com.iflytek.rec.anchorpredic.pojo.AnchorPredictParams;
import com.iflytek.rec.anchorpredic.pojo.AnchorPredictResponse;
import com.iflytek.rec.anchorpredic.pojo.AnchorPredictReturn;
import com.iflytek.rec.anchorpredic.pojo.ModelParams;
import com.iflytek.rec.anchorpredic.utils.BertFullTokenizerExdFour;
import com.iflytek.rec.anchorpredic.utils.BertFullTokenizerNew;
import com.iflytek.rec.anchorpredic.utils.Constant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.iflytek.rec.anchorpredic.utils.Constant.*;


/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/10/13 14:39
 */
public class AnchorPredictEngineServiceImpl implements IAnchorPredictEngineService {
    private static final Logger log = LoggerFactory.getLogger(AnchorPredicMethodImpl.class);


    private static final IAnchorPredictMethod anchorPredictMethod = new AnchorPredicMethodImpl();


    @Override
    public void initModel(ModelParams modelParams) {
        try {
            Instant time1 = Instant.now();
            ModelHolder.of(modelParams.getWorkPath()).init(modelParams);
            Instant time2 = Instant.now();
            log.info("【锚点预测引擎服务】模型加载成功！耗时:{}", ChronoUnit.MILLIS.between(time1, time2));
        } catch (Exception e) {
            log.error("【锚点预测引擎服务】模型加载失败！- {}", e.toString());
        }
    }

    @Override
    public AnchorPredictReturn anchorPredictEngineService(AnchorPredictParams anchorPredictParams) {
        log.info("【锚点预测引擎服务】开始!traceId={}", anchorPredictParams.getTraceId());
        AnchorPredictReturn anchorPredictReturn = new AnchorPredictReturn();
        Map<String, String> anchorPredictReturnEmpty = new HashMap<>();
        List<String> topics = new ArrayList<>(anchorPredictParams.getHtmlStrings().keySet());
        Instant timeS = Instant.now();
        InferResult inferResult = new InferResult();
        String resourcePattern = anchorPredictParams.getUserInfo().getSubjectCode() + "_" + anchorPredictParams.getUserInfo().getPhaseCode();

        try {
            InferParam inferParam = topicBodyToModelParams(anchorPredictParams, resourcePattern, anchorPredictReturnEmpty);
            Instant time1 = Instant.now();
            log.info("【锚点预测引擎服务】模型参数解析时间 cost= {} ms", ChronoUnit.MILLIS.between(timeS, time1));
            if (StringUtils.equals(resourcePattern, Constant.PRIMARY_PATTERN)) {
                log.info("【锚点预测引擎服务】小学数学锚点预测");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.PRIMARYSCHOOLMATH.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, Constant.JUNIOR_PATTERN)) {
                log.info("【锚点预测引擎服务】初中数学锚点预测");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.JUNIORSCHOOLMATH.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, JUNIOR_PHYSICS_PATTERN)) {
                log.info("【锚点预测引擎服务】初中物理锚点预测");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.JUNIORSCHOOLPHYSICS.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, HIGH_PHYSICS_PATTERN)) {
                log.info("【锚点预测引擎服务】高中物理锚点预测");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.HIGHSCHOOLPHYSICS.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, HIGH_MATH_PATTERN)) {
                log.info("【锚点预测引擎服务】高中数学锚点预测");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.HIGHSCHOOLMATH.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, HIGH_CHEMISTRY_PATTERN)) {
                log.info("【锚点预测引擎服务】高中化学锚点预测");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.HIGHSCHOOLCHEMISTRY.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, JUNIOR_CHEMISTRY_PATTERN)) {
                log.info("【锚点预测引擎服务】初中化学锚点预测");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.JUNIORSCHOOLCHEMISTRY.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, JUNIOR_BIOLOGY_PATTERN)) {
                log.info("【锚点预测引擎服务】初中生物锚点预测");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.JUNIORSCHOOLBIOLOGY.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, HIGH_BIOLOGY_PATTERN)) {
                log.info("【锚点预测引擎服务】高中生物锚点预测");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.HIGHSCHOOLBIOLOGY.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, PRIMARY_SCIENCE_PATTERN)) {
                log.info("【锚点预测引擎服务】小学科学锚点预测");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.PRIMARYSCHOOLSCIENCE.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, JUNIOR_SCIENCE_PATTERN)) {
                log.info("【锚点预测引擎服务】初中科学锚点预测");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.JUNIORSCHOOLSCIENCE.getModelScope(), inferParam);
            } else {
                log.error("【锚点预测引擎服务】传入学科:{},学段:{},不支持的学科学段！", anchorPredictParams.getUserInfo().getSubjectCode(), anchorPredictParams.getUserInfo().getPhaseCode());
            }
            Instant time2 = Instant.now();
//            log.debug("【锚点预测引擎服务】模型推理结束,模型输出:{}", JSON.toJSONString(inferResult));
            log.info("【锚点预测引擎服务】模型推理结束,模型输出题目数量:{}", inferResult.getInferContent().size());
            log.info("【锚点预测引擎服务】模型推理时间 cost= {} ms", ChronoUnit.MILLIS.between(timeS, time2));

        } catch (Exception e) {
            log.error("【锚点预测引擎服务】模型推理异常错误！{}", e.toString());
            anchorPredictReturn.setAnchorPredictResult(new HashMap<>());
            return anchorPredictReturn;
        }

        try {
            Instant time3 = Instant.now();
            List<Content> modelTopicsAnchorsScore = inferResult.getInferContent();
            Map<String, Map<String, Float>> anchorPredictModelOutAnalysis = anchorPredictMethod.anchorPredictModelOutAnalysis(modelTopicsAnchorsScore, topics, resourcePattern);
            Instant time4 = Instant.now();
            log.info("【锚点预测引擎服务】模型推理结果解析时间 cost= {} ms", ChronoUnit.MILLIS.between(time3, time4));
            Map<String, Map<String, Float>> anchorPredictFilterBySpv = AnchorPredicMethodImpl.anchorFilterBySpv(anchorPredictParams, anchorPredictModelOutAnalysis, resourcePattern);
            Instant time45 = Instant.now();
            log.info("【锚点预测引擎服务】当前支持锚点过滤+按教材版本过滤耗时 cost= {} ms", ChronoUnit.MILLIS.between(time4, time45));
            Map<String, Map<String, Float>> anchorPredictFilter = anchorPredictMethod.anchorPredictFilter(anchorPredictParams, anchorPredictFilterBySpv, resourcePattern);
            Instant time5 = Instant.now();
            log.info("【锚点预测引擎服务】锚点过滤模块耗时 cost= {} ms", ChronoUnit.MILLIS.between(time45, time5));
            Map<String, String> result = anchorPredictMethod.anchorPredictVote(anchorPredictParams, anchorPredictFilterBySpv, anchorPredictFilter, resourcePattern);
            Instant time6 = Instant.now();
            log.info("【锚点预测引擎服务】锚点投票模块耗时 cost= {} ms", ChronoUnit.MILLIS.between(time5, time6));
            for (String topic : topics) {
                if (result.get(topic) == null) {
                    log.info("【锚点预测引擎服务】锚点投票后试题 {},锚点为空,返回空字符串", topic);
                    result.put(topic, "");
                }
            }
            anchorPredictReturn.setAnchorPredictResult(result);

        } catch (Exception e) {
            log.error("【锚点预测引擎服务】过滤|投票异常错误！{}", e.toString());
            anchorPredictReturn.setAnchorPredictResult(new HashMap<>());
            return anchorPredictReturn;
        }

        Instant timeE = Instant.now();
        //据识逻辑 返回空字符串
        if (!anchorPredictReturnEmpty.isEmpty() && !anchorPredictReturn.getAnchorPredictResult().isEmpty()) {
            for (String id : anchorPredictReturnEmpty.keySet()) {
                anchorPredictReturn.getAnchorPredictResult().put(id, "");
            }
        }
        log.info("【锚点预测引擎服务】结果返回：{}", JSON.toJSONString(anchorPredictReturn));
        log.info("【锚点预测引擎服务】总时间 cost= {} ms", ChronoUnit.MILLIS.between(timeS, timeE));
        log.info("【锚点预测引擎服务】结束!traceId={}", anchorPredictParams.getTraceId());

        return anchorPredictReturn;
    }


    @Override
    public AnchorPredictResponse anchorPredictTopNEngine(AnchorPredictParams anchorPredictParams) {
        log.info("【锚点预测引擎服务】开始!traceId={}", anchorPredictParams.getTraceId());
        AnchorPredictResponse anchorPredictReturn = new AnchorPredictResponse();
        Map<String, String> anchorPredictReturnEmpty = new HashMap<>();
        List<String> topics = new ArrayList<>(anchorPredictParams.getHtmlStrings().keySet());
        Instant timeS = Instant.now();
        InferResult inferResult = new InferResult();
        String resourcePattern = anchorPredictParams.getUserInfo().getSubjectCode() + "_" + anchorPredictParams.getUserInfo().getPhaseCode();

        try {
            InferParam inferParam = topicBodyToModelParams(anchorPredictParams, resourcePattern, anchorPredictReturnEmpty);
            Instant time1 = Instant.now();
            log.info("【锚点预测引擎服务】模型参数解析时间 cost= {} ms", ChronoUnit.MILLIS.between(timeS, time1));
            if (StringUtils.equals(resourcePattern, Constant.PRIMARY_PATTERN)) {
                log.info("【锚点预测引擎服务】小学数学锚点预测topN");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.PRIMARYSCHOOLMATH.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, Constant.JUNIOR_PATTERN)) {
                log.info("【锚点预测引擎服务】初中数学锚点预测topN");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.JUNIORSCHOOLMATH.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, JUNIOR_PHYSICS_PATTERN)) {
                log.info("【锚点预测引擎服务】初中物理锚点预测topN");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.JUNIORSCHOOLPHYSICS.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, HIGH_PHYSICS_PATTERN)) {
                log.info("【锚点预测引擎服务】高中物理锚点预测topN");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.HIGHSCHOOLPHYSICS.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, HIGH_MATH_PATTERN)) {
                log.info("【锚点预测引擎服务】高中数学锚点预测topN");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.HIGHSCHOOLMATH.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, HIGH_CHEMISTRY_PATTERN)) {
                log.info("【锚点预测引擎服务】高中化学锚点预测topN");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.HIGHSCHOOLCHEMISTRY.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, JUNIOR_CHEMISTRY_PATTERN)) {
                log.info("【锚点预测引擎服务】初中化学锚点预测topN");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.JUNIORSCHOOLCHEMISTRY.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, JUNIOR_BIOLOGY_PATTERN)) {
                log.info("【锚点预测引擎服务】初中生物锚点预测topN");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.JUNIORSCHOOLBIOLOGY.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, HIGH_BIOLOGY_PATTERN)) {
                log.info("【锚点预测引擎服务】高中生物锚点预测topN");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.HIGHSCHOOLBIOLOGY.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, PRIMARY_SCIENCE_PATTERN)) {
                log.info("【锚点预测引擎服务】小学科学锚点预测topN");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.PRIMARYSCHOOLSCIENCE.getModelScope(), inferParam);
            } else if (StringUtils.equals(resourcePattern, JUNIOR_SCIENCE_PATTERN)) {
                log.info("【锚点预测引擎服务】初中科学锚点预测topN");
                inferResult = ModelHolder.inferHelper.predict(ModelScopeEnum.JUNIORSCHOOLSCIENCE.getModelScope(), inferParam);
            } else {
                log.error("【锚点预测引擎服务】传入学科:{},学段:{},不支持的学科学段！", anchorPredictParams.getUserInfo().getSubjectCode(), anchorPredictParams.getUserInfo().getPhaseCode());
            }
            Instant time2 = Instant.now();
//            log.debug("【锚点预测引擎服务】模型推理结束,模型输出:{}", JSON.toJSONString(inferResult));
            log.info("【锚点预测引擎服务】模型推理结束,模型输出题目数量:{}", inferResult.getInferContent().size());
            log.info("【锚点预测引擎服务】模型推理时间 cost= {} ms", ChronoUnit.MILLIS.between(timeS, time2));

        } catch (Exception e) {
            log.error("【锚点预测引擎服务】模型推理异常错误！{}", e.toString());
            anchorPredictReturn.setAnchorPredictResult(new HashMap<>());
            return anchorPredictReturn;
        }

        try {
            Instant time3 = Instant.now();
            List<Content> modelTopicsAnchorsScore = inferResult.getInferContent();
            Map<String, Map<String, Float>> anchorPredictModelOutAnalysis = anchorPredictMethod.anchorPredictModelOutAnalysis(modelTopicsAnchorsScore, topics, resourcePattern);
            Instant time4 = Instant.now();
            log.info("【锚点预测引擎服务】模型推理结果解析时间 cost= {} ms", ChronoUnit.MILLIS.between(time3, time4));
            Map<String, Map<String, Float>> anchorPredictFilterBySpv = AnchorPredicMethodImpl.anchorFilterBySpvTopN(anchorPredictParams, anchorPredictModelOutAnalysis, resourcePattern);
            Instant time45 = Instant.now();
            log.info("【锚点预测引擎服务】当前支持锚点过滤+按教材版本过滤耗时 cost= {} ms", ChronoUnit.MILLIS.between(time4, time45));
            Map<String, Map<String, Float>> anchorPredictFilter = anchorPredictMethod.anchorPredictFilter(anchorPredictParams, anchorPredictFilterBySpv, resourcePattern);
            Instant time5 = Instant.now();
            log.info("【锚点预测引擎服务】锚点过滤模块耗时 cost= {} ms", ChronoUnit.MILLIS.between(time45, time5));
            Map<String, List<String>> result = anchorPredictMethod.anchorPredictVoteTopN(anchorPredictParams, anchorPredictFilterBySpv, anchorPredictFilter, resourcePattern);
            Instant time6 = Instant.now();
            log.info("【锚点预测引擎服务】锚点投票模块耗时 cost= {} ms", ChronoUnit.MILLIS.between(time5, time6));
            for (String topic : topics) {
                if (result.get(topic) == null) {
                    log.info("【锚点预测引擎服务】锚点投票后试题 {},锚点为空,返回空列表", topic);
                    result.put(topic, new ArrayList<>());
                }
            }
            anchorPredictReturn.setAnchorPredictResult(result);

        } catch (Exception e) {
            log.error("【锚点预测引擎服务】过滤|投票异常错误！{}", e.toString());
            anchorPredictReturn.setAnchorPredictResult(new HashMap<>());
            return anchorPredictReturn;
        }

        Instant timeE = Instant.now();
        //据识逻辑 返回空字符串
        if (!anchorPredictReturnEmpty.isEmpty() && !anchorPredictReturn.getAnchorPredictResult().isEmpty()) {
            for (String id : anchorPredictReturnEmpty.keySet()) {
                anchorPredictReturn.getAnchorPredictResult().put(id, new ArrayList<>());
            }
        }
        log.info("【锚点预测引擎服务】结果返回：{}", JSON.toJSONString(anchorPredictReturn));
        log.info("【锚点预测引擎服务】总时间 cost= {} ms", ChronoUnit.MILLIS.between(timeS, timeE));
        log.info("【锚点预测引擎服务】结束!traceId={}", anchorPredictParams.getTraceId());

        return anchorPredictReturn;
    }
    @Override
    public void closeModel() {
        try {
            ModelHolder.inferHelper.fini();
            log.info("【锚点预测引擎服务】模型释放成功！");
        } catch (Exception e) {
            log.error("【锚点预测引擎服务】模型释放失败！ - {}", e.toString());
        }
    }

    /**
     * 解析题干信息  bert编码为锚点预测模型格式
     * 构造锚点预测模型入参
     */
    public static InferParam topicBodyToModelParams(AnchorPredictParams anchorPredictParams, String resourcePattern, Map<String, String> anchorPredictReturnEmpty) {

        List<Content> contents = new ArrayList<>();
        InferParam inferParam = new InferParam();
        inferParam.setInferContent(contents);

        Map<String, String> htmlStrings = anchorPredictParams.getHtmlStrings();
        Map<String, String> ocrStrings = anchorPredictParams.getOcrStrings();
        String paraAnalysisPattern = StringUtils.equals(resourcePattern, HIGH_MATH_PATTERN) ? HIGH_MATH_PATTERN : JUNIOR_PHYSICS_PATTERN;
        for (Map.Entry<String, String> entry : htmlStrings.entrySet()) {
            List<DataFrame> dataFrames = new ArrayList<>();
            Content content = new Content();
            content.setInputs(dataFrames);
            DataFrame token = new DataFrame();
            DataFrame mask = new DataFrame();
            DataFrame word = new DataFrame();
            dataFrames.add(token);
            dataFrames.add(mask);
            dataFrames.add(word);

            String htmlTpoic;
            if (entry.getValue().isEmpty()) {
                htmlTpoic = "";
            } else {
//                log.info("html:{}",entry.getValue());
                //小数和初数都采用物理处理方式
//                htmlTpoic = ParamAnalysisHolder.of(JUNIOR_PHYSICS_PATTERN, "", entry.getValue());
                htmlTpoic = ParamAnalysisHolder.of(paraAnalysisPattern, "", entry.getValue());
            }

            String ocrTpoic;
            if (ocrStrings.get(entry.getKey()).isEmpty()) {
                ocrTpoic = "";
            } else {
//                log.info("ocr:{}",ocrStrings.get(entry.getKey()));
                //小数和初数都采用物理处理方式
//                ocrTpoic = ParamAnalysisHolder.of(JUNIOR_PHYSICS_PATTERN, Constant.TOPIC_OCR_TYPE, ocrStrings.get(entry.getKey()));
                ocrTpoic = ParamAnalysisHolder.of(paraAnalysisPattern, Constant.TOPIC_OCR_TYPE, ocrStrings.get(entry.getKey()));
            }
            String longTpoicString = htmlTpoic;
            if (htmlTpoic.length() <= ocrTpoic.length()) {
                log.info("【锚点预测引擎服务】选择ocr文本作为模型输入：{}", ocrTpoic);
                longTpoicString = ocrTpoic;
            } else {
                if (anchorPredictParams.getCfdsLevels().getOrDefault(entry.getKey(), 0d) > 0.9d || ocrTpoic.isEmpty()) {
                    log.info("【锚点预测引擎服务】html阈值={}(为null默认为0)>0.9,选择html文本作为模型输入：{}", anchorPredictParams.getCfdsLevels().get(entry.getKey()), htmlTpoic);
                } else {
                    log.info("【锚点预测引擎服务】html阈值<=0.9,选择ocr文本作为模型输入：{}", ocrTpoic);
                    longTpoicString = ocrTpoic;
                }
            }

            //兜底 解析为空 据识返空
            if (longTpoicString.isEmpty()) {
                log.info("解析后={}题干为空 据识返空", entry.getKey());
                anchorPredictReturnEmpty.put(entry.getKey(), "");
            }

            String longTpoicStringWordStatics = longTpoicString;
            longTpoicString = longTpoicStringCut(longTpoicString, 2);
            longTpoicStringWordStatics = longTpoicStringCut(longTpoicStringWordStatics, 0);
            log.info("【锚点预测引擎服务】截取后文本={}", longTpoicString);

            List<Long> topicBert;
            List<Long> wordStatics;
            List<Long> masks;
            if(EXD_FOUR_SUB.contains(resourcePattern)){
                topicBert = new ArrayList<>(BertFullTokenizerExdFour.getTokensIndex(BertFullTokenizerExdFour.getBertFullTokenizer(longTpoicString, true)));
                topicBert = vectorLenChange(topicBert, INFER_LENGTH_100);
                wordStatics = BertFullTokenizerExdFour.getWordNums(longTpoicStringWordStatics, resourcePattern);
                masks = BertFullTokenizerExdFour.getMask(topicBert);
            }else {
                topicBert = new ArrayList<>(BertFullTokenizerNew.getTokensIndex(BertFullTokenizerNew.getBertFullTokenizer(longTpoicString, true)));
                topicBert = vectorLenChange(topicBert, INFER_LENGTH_100);
                wordStatics = BertFullTokenizerNew.getWordNums(longTpoicStringWordStatics, resourcePattern);
                masks = BertFullTokenizerNew.getMask(topicBert);
            }

//            log.info("【锚点预测引擎服务】试题={}，topicBert={}", entry.getKey(), topicBert);
//            log.info("【锚点预测引擎服务】试题={}，wordStatics={}", entry.getKey(), wordStatics);



            token.setInt64Value(topicBert);
            mask.setInt64Value(masks);
            word.setInt64Value(wordStatics);

            token.setShape(Collections.singletonList(topicBert.size()));
            mask.setShape(Collections.singletonList(masks.size()));
            word.setShape(Collections.singletonList(wordStatics.size()));

            token.setFrameType(InferDataTypeEnum.DATA_TYPE_INT64);
            mask.setFrameType(InferDataTypeEnum.DATA_TYPE_INT64);
            word.setFrameType(InferDataTypeEnum.DATA_TYPE_INT64);
            contents.add(content);
        }
//        log.debug("【锚点预测引擎服务】模型java入参:{}", JSON.toJSONString(inferParam));
        return inferParam;
    }

    private static List<Long> vectorLenChange(List<Long> topicBert, int inferLength) {
        if (topicBert.size() > inferLength) {
//            log.debug("【锚点预测tokenizer】分词index结果长度大于{},裁剪为{}", inferLength, inferLength);
            topicBert = topicBert.subList(0, inferLength - 1);
            topicBert.add(102L);
        } else {
//            log.debug("【锚点预测tokenizer】分词index结果长度大于{},裁剪为{}", inferLength, inferLength);
            //长度不够 其后补0
            while (topicBert.size() != inferLength) {
                topicBert.add(0L);
            }
        }
        return topicBert;
    }

    private static String longTpoicStringCut(String longTpoicString, Integer end) {
        String resStr = longTpoicString;
        if (StringUtils.isEmpty(longTpoicString)) {
            log.warn("longTpoicString为:{}", longTpoicString);

        } else {
            int len = longTpoicString.length();
            if (len >= INFER_LENGTH_100) {
                resStr = longTpoicString.substring(0, 25) + longTpoicString.substring(len - 75, len - end);
            }
        }
        return resStr;
    }

}
