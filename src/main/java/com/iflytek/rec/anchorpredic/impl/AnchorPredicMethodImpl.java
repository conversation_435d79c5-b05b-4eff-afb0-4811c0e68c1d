package com.iflytek.rec.anchorpredic.impl;

import com.alibaba.fastjson2.JSON;
import com.iflytek.infer.domain.Content;
import com.iflytek.rec.anchorpredic.datafactory.holder.FileReaderUtil;
import com.iflytek.rec.anchorpredic.interfaces.IAnchorPredictMethod;
import com.iflytek.rec.anchorpredic.method.AnalysLatexUMap;
import com.iflytek.rec.anchorpredic.method.OriginalLatex;
import com.iflytek.rec.anchorpredic.method.regexp.CommenRegExp;
import com.iflytek.rec.anchorpredic.pojo.AnchorPredictParams;
import com.iflytek.rec.anchorpredic.utils.BertFullTokenizer;
import com.iflytek.rec.anchorpredic.utils.Constant;
import com.iflytek.rec.anchorpredic.utils.DigitalToChineseUtil;
import com.iflytek.rec.anchorpredic.utils.Operators;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.iflytek.rec.anchorpredic.method.AnalysLatexUMap.latex;
import static com.iflytek.rec.anchorpredic.method.AnalysLatexUMap.latexMapOpt;
import static com.iflytek.rec.anchorpredic.utils.Constant.ONE;
import static com.iflytek.rec.anchorpredic.utils.Constant.anchorMagic;


/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/9/26 15:12
 */
public class AnchorPredicMethodImpl implements IAnchorPredictMethod {

    private static final Logger log = LoggerFactory.getLogger(AnchorPredicMethodImpl.class);
    private static final List<String> OPERATOR = Arrays.asList("+", "-", "×", "÷", "=", "\\", "一十", "/");
    private static final List<String> OPERATOR_CH = Arrays.asList("加", "减", "乘以", "除以", "等于", "除以", "十", "比");
    //去除题号和得分  保留中间题干
    private static final String PATTERN_TOPIC_BODY = "^(\\d{1}\\.|\\(\\d+\\))|\\([\\r\\n\\s]*\\d+[\\r\\n\\s]*分[\\r\\n\\s]*\\)";
    //整数和小数转为汉字
    private static final String PATTERN_NUMS_TO_CH = "\\d+\\.[1-9]+|\\d+";
    private static final String PATTERN_NUMS_PHYSICS_TO_CH = "\\d+\\.\\d+|\\d+";
    //+-×÷=映射为汉字：加减乘除等于
    private static final String PATTERN_OPERATOR = "[+-×÷=.\\\\]|一十";
    private static final String PATTERN_OPERATOR_PHYSICS = "[+\\-×÷=/]|一十";
    //[一二三四五六]年级    [上下]册
    private static final String PATTERN_SEMESTER = "[一二三四五六七八九]年级[上下]册";

    private static final Pattern pBody = Pattern.compile(PATTERN_TOPIC_BODY);

    private static final Pattern pNum = Pattern.compile(PATTERN_NUMS_TO_CH);
    private static final Pattern pNumPhysics = Pattern.compile(PATTERN_NUMS_PHYSICS_TO_CH);

    private static final Pattern pOperator = Pattern.compile(PATTERN_OPERATOR);
    private static final Pattern pOperatorPhysics = Pattern.compile(PATTERN_OPERATOR_PHYSICS);
    private static final Pattern pSemester = Pattern.compile(PATTERN_SEMESTER);

    //后处理规则
    private static final String REGEX2 = "\\\\[a-z]+\\s*\\{(.*?)\\}";
    private static final String REGEX3 = "\\\\left|\\\\right|\\\\rm|\\\\";
    private static final String REGEX4 = "\\{(.*?)\\}";
    private static final String REGEX5 = "\\s\\s+";
    private static final Pattern p2 = Pattern.compile(REGEX2);
    private static final Pattern p3 = Pattern.compile(REGEX3);
    private static final Pattern p4 = Pattern.compile(REGEX4);
    private static final Pattern p5 = Pattern.compile(REGEX5);

    @Override
    public String dataAnalysisExceptLatex(String htmlString) throws IOException {

        StringBuilder topic_body = OriginalLatex.originalLatex(htmlString);
        //去除题号和得分  保留中间题干
        StringBuffer sb = patternBody(String.valueOf(topic_body));
        //整数和小数转为汉字
        StringBuffer sb1 = patternNumToWorld(sb);
        log.info("【锚点预测method-参数解析】入参htmlString解析结果：{}", sb1.toString());
        return sb1.toString();
    }

    @Override
    public String dataAnalysisIncludeLatexUMap(String htmlString, String resourcePattern) throws IOException {
        htmlString = htmlString.replaceAll("\\uD835\\uDF0B", "π");
        htmlString = htmlString.replaceAll("&nbsp;|&nbsp|\\\\textcelsius", "");
        htmlString = htmlString.replaceAll("\\\\endmatrix|\\\\beginmatrix", "");
        StringBuilder topic_body = AnalysLatexUMap.analysLatexUMap(htmlString, resourcePattern);
//        去除题号和得分  保留中间题干
        StringBuffer sb = CommenRegExp.regexp(String.valueOf(topic_body));
        //整数和小数转为汉字
        StringBuffer sb1 = patternNumToWorld(sb);
        //+-×÷=映射为汉字：加减乘除等于
        String sb2 = patternOperatorToWorld(sb1);
        sb2 = patternFinalProcess(sb2);
        return sb2;
    }

    @Override
    public String dataAnalysisOcr(String ocrString, String resourcePattern) {
        ocrString = ocrString.replaceAll("\\uD835\\uDF0B", "π");
        ocrString = ocrString.replaceAll("\\\\pi", "π");
        ocrString = ocrString.replaceAll("&nbsp;|&nbsp|\\\\cdots|\\\\textcelsius", "");
        ocrString = ocrString.replaceAll("\\\\endmatrix|\\\\beginmatrix", "");
        ocrString = latex(ocrString, resourcePattern);
        Pattern paBody = Pattern.compile("ifly-latex-begin(.+)ifly-latex-end");
        Matcher ma = paBody.matcher(ocrString);
        StringBuffer sbf = new StringBuffer();
        while (ma.find()) {
            String str = latexMapOpt(ma.group(1), resourcePattern);
            str = str.replaceAll("\\\\+", "\\\\\\\\");
            ma.appendReplacement(sbf, str);
        }
        ma.appendTail(sbf);
        String ocrLatexAnalysis = sbf.toString();
        //去除题号和得分  保留中间题干
        StringBuffer sb = CommenRegExp.regexp(ocrLatexAnalysis);
        //整数和小数转为汉字
        StringBuffer sb1 = patternNumToWorld(sb);
        //+-×÷=映射为汉字：加减乘除等于
        String sb2 = patternOperatorToWorld(sb1);
        sb2 = patternFinalProcess(sb2);
        return sb2;
    }

    public static String dataAnalysisOcrJson(String ocrString) {
        String ocrNolable = ocrString.replace("ifly-latex-begin", "").replace("ifly-latex-end", "");
        String ocrLatexAnalysis = AnalysLatexUMap.latexMap(ocrNolable, "02_03");
        return ocrLatexAnalysis;
    }

    @Override
    public Map<String, Map<String, Float>> anchorPredictModelOutAnalysis(List<Content> modelTopicsAnchorsScore, List<String> topics, String resourcePattern) {
        Map<String, Map<String, Float>> topicsAnchorsScoreMap = new HashMap<>();
        int t = 0;
        int tSize = topics.size();
        for (Content content : modelTopicsAnchorsScore) {
            int i = 0;
            Map<String, Float> anchorsScore = new HashMap<>();
            if (content.getOutputs().isEmpty()) {
                continue;
            }
            for (Float anchorScore : content.getOutputs().get(0).getFloatValue()) {
//                log.info("第{}题,模型输出得分={}", t + 1, content.getOutputs().get(0).getFloatValue());
                try {
                    for (String repeatAnchor : FileReaderUtil.of(resourcePattern).getIndexToAnchorIdRepeat().get(i)) {
                        anchorsScore.put(repeatAnchor, anchorScore);
                    }
                    i++;
                    //模型输出维度大于等于锚点体系长度时 终止
                    if (i >= FileReaderUtil.of(resourcePattern).getAnchorIdToIndexs().size()) {
                        break;
                    }
                } catch (Exception e) {
                    log.error("【锚点预测method-模型预测】模型预测锚点数量超出缓存数量!");
                    e.printStackTrace();
                }

            }
            //防止模型输出和入参试题数量不一致
            if (t < tSize) {
                topicsAnchorsScoreMap.put(topics.get(t), anchorsScore);
                t++;
            } else {
                log.warn("【锚点预测method-模型预测】模型输出题目与入参题目不一致！无模型预测结果试题走后续兜底逻辑！");
                topicsAnchorsScoreMap.put(topics.get(tSize - 1), anchorsScore);
            }
        }
//        log.debug("【锚点预测method-模型预测】模型输出结果解析为map：{},锚点数量：{}", topicsAnchorsScoreMap, topicsAnchorsScoreMap.get(topicsAnchorsScoreMap.entrySet().iterator().next().getKey()).size());

        return topicsAnchorsScoreMap;
    }


    @Override
    public Map<String, Map<String, Float>> anchorPredictFilter(AnchorPredictParams anchorPredictParams, Map<String, Map<String, Float>> topicsAnchorsScoreMap, String resourcePattern) {
        //当前支持锚点过滤+按教材版本过滤
        Map<String, Map<String, Float>> anchorFilter = new HashMap<>(topicsAnchorsScoreMap);

        //正则匹配教辅名称-也即 学期
        List<String> semesters = patternSemester(anchorPredictParams.getFullPageSearchRes());
        log.info("【锚点预测method-过滤模块】正则匹配学期信息：{}", semesters);
        //有学期
        if (!semesters.isEmpty()) {
            //按照学期过滤
            semesterOrChapterFilter(anchorFilter, Objects.requireNonNull(FileReaderUtil.of(resourcePattern)).getAnchorIdToSemester(), semesters.get(0));
            log.info("【锚点预测method-过滤模块】正则匹配存在学期信息【{}】,按照学期过滤后,锚点数量size：{}", semesters.get(0), anchorFilter.entrySet().iterator().next().getValue().size());
        }
        anchorFilter = getMaxScoreAnchor(anchorFilter, anchorPredictParams);
        return anchorFilter;
    }

    @Override
    public Map<String, String> anchorPredictVote(AnchorPredictParams anchorPredictParams, Map<String, Map<String, Float>> topicsAnchorsScoreMap, Map<String, Map<String, Float>> topicsAnchorsScoreMapFilter, String resourcePattern) {
        Map<String, Map<String, Float>> topicAnchorScoreResult = new HashMap<>();
        //统计当前范围内概率最高的锚点所对应的章节
        Map<String, Boolean> chapterVerify = semesterOrChaperVerify(topicsAnchorsScoreMapFilter, Objects.requireNonNull(FileReaderUtil.of(resourcePattern)).getAnchorIdToChapter(), "chapterVerify");
        log.info("【锚点预测method-投票模块】统计当前范围内概率最高的锚点所对应的章节：{}\n", chapterVerify);
        boolean isChapter = false;
        if (!chapterVerify.isEmpty()) {
            isChapter = null != chapterVerify.entrySet().iterator().next().getValue() ? chapterVerify.entrySet().iterator().next().getValue() : false;

        }
        if (isChapter) {
            //按照章节过滤
            semesterOrChapterFilter(topicsAnchorsScoreMap, Objects.requireNonNull(FileReaderUtil.of(resourcePattern)).getAnchorIdToChapter(), chapterVerify.entrySet().iterator().next().getKey());
//            log.debug("【锚点预测method-投票模块】存在top章节,按照章节过滤结果：{}", topicsAnchorsScoreMap);
            topicAnchorScoreResult = getMaxScoreAnchor(topicsAnchorsScoreMap, anchorPredictParams);
            log.info("【锚点预测method-投票模块】存在top章节,选择当前范围内概率最高的锚点：{}", topicAnchorScoreResult);
        } else {
            log.info("【锚点预测method-投票模块】不存在top章节,选择过滤后范围内概率最高的锚点：{}", topicsAnchorsScoreMapFilter);
            topicAnchorScoreResult = topicsAnchorsScoreMapFilter;
        }

        Map<String, String> topicAnchorResult = new HashMap<>();

        for (Map.Entry<String, Map<String, Float>> mapEntry : topicAnchorScoreResult.entrySet()) {
            String anchor = mapEntry.getValue().entrySet().iterator().next().getKey();
            if (anchor.isEmpty()) {
                log.info("【锚点预测method-投票模块】该题:{} 下无锚点,返回空字符串", mapEntry.getKey());
                topicAnchorResult.put(mapEntry.getKey(), "");
                continue;
            }
            //拒识模块
            if (StringUtils.equals(resourcePattern, Constant.JUNIOR_PHYSICS_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), "");
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), anchorNew);
            } else if (StringUtils.equals(resourcePattern, Constant.PRIMARY_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.37,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), "");
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), anchorNew);
            } else if (StringUtils.equals(resourcePattern, Constant.JUNIOR_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.3,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), "");
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), anchorNew);
            } else if (StringUtils.equals(resourcePattern, Constant.JUNIOR_CHEMISTRY_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), "");
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), anchorNew);
            } else if (StringUtils.equals(resourcePattern, Constant.HIGH_MATH_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), "");
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), anchorNew);
            } else if (StringUtils.equals(resourcePattern, Constant.HIGH_CHEMISTRY_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), "");
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), anchorNew);
            } else if (StringUtils.equals(resourcePattern, Constant.HIGH_PHYSICS_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), "");
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), anchorNew);
            } else if (StringUtils.equals(resourcePattern, Constant.JUNIOR_BIOLOGY_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), "");
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), anchorNew);
            } else if (StringUtils.equals(resourcePattern, Constant.HIGH_BIOLOGY_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), "");
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), anchorNew);
            } else if (StringUtils.equals(resourcePattern, Constant.PRIMARY_SCIENCE_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), "");
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), anchorNew);
            } else if (StringUtils.equals(resourcePattern, Constant.JUNIOR_SCIENCE_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), "");
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), anchorNew);
            } else {
                log.error("【锚点预测method-投票模块】无此学科学段{}", resourcePattern);
            }

        }
        log.info("【锚点预测method-投票模块】最终投票结果：{}", topicAnchorResult);
        return topicAnchorResult;
    }

    @Override
    public Map<String, List<String>> anchorPredictVoteTopN(AnchorPredictParams anchorPredictParams, Map<String, Map<String, Float>> topicsAnchorsScoreMap, Map<String, Map<String, Float>> topicsAnchorsScoreMapFilter, String resourcePattern) {
        Map<String, Map<String, Float>> topicAnchorScoreResult;
        Map<String, List<String>> topicAnchorResult = new HashMap<>();

        if (anchorPredictParams.getTopN() > ONE) {
            topicAnchorScoreResult = getMaxScoreAnchorTopN(topicsAnchorsScoreMap, anchorPredictParams);
            log.info("【锚点预测method-投票模块】入参topN={}", anchorPredictParams.getTopN());
            for (Map.Entry<String, Map<String, Float>> mapEntry : topicAnchorScoreResult.entrySet()) {
                topicAnchorResult.putIfAbsent(mapEntry.getKey(), new ArrayList<>());
                for (Map.Entry<String, Float> anchorCandas : mapEntry.getValue().entrySet()) {
                    topicAnchorResult.get(mapEntry.getKey()).add(anchorCandas.getKey());
                }
            }
            return topicAnchorResult;
        }

        log.info("【锚点预测method-投票模块】入参topN=1");
        //统计当前范围内概率最高的锚点所对应的章节
        Map<String, Boolean> chapterVerify = semesterOrChaperVerify(topicsAnchorsScoreMapFilter, Objects.requireNonNull(FileReaderUtil.of(resourcePattern)).getAnchorIdToChapter(), "chapterVerify");
        log.info("【锚点预测method-投票模块】统计当前范围内概率最高的锚点所对应的章节：{}\n", chapterVerify);
        boolean isChapter = false;
        if (!chapterVerify.isEmpty()) {
            isChapter = null != chapterVerify.entrySet().iterator().next().getValue() ? chapterVerify.entrySet().iterator().next().getValue() : false;

        }
        if (isChapter) {
            //按照章节过滤
            semesterOrChapterFilter(topicsAnchorsScoreMap, Objects.requireNonNull(FileReaderUtil.of(resourcePattern)).getAnchorIdToChapter(), chapterVerify.entrySet().iterator().next().getKey());
//            log.debug("【锚点预测method-投票模块】存在top章节,按照章节过滤结果：{}", topicsAnchorsScoreMap);
            topicAnchorScoreResult = getMaxScoreAnchor(topicsAnchorsScoreMap, anchorPredictParams);
            log.info("【锚点预测method-投票模块】存在top章节,选择当前范围内概率最高的锚点：{}", topicAnchorScoreResult);
        } else {
            log.info("【锚点预测method-投票模块】不存在top章节,选择过滤后范围内概率最高的锚点：{}", topicsAnchorsScoreMapFilter);
            topicAnchorScoreResult = topicsAnchorsScoreMapFilter;
        }


        for (Map.Entry<String, Map<String, Float>> mapEntry : topicAnchorScoreResult.entrySet()) {
            String anchor = mapEntry.getValue().entrySet().iterator().next().getKey();
            if (anchor.isEmpty()) {
                log.info("【锚点预测method-投票模块】该题:{} 下无锚点,返回空字符串", mapEntry.getKey());
                topicAnchorResult.put(mapEntry.getKey(), new ArrayList<>());
                continue;
            }
            //拒识模块
            if (StringUtils.equals(resourcePattern, Constant.JUNIOR_PHYSICS_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), new ArrayList<>());
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), Collections.singletonList(anchorNew));
            } else if (StringUtils.equals(resourcePattern, Constant.PRIMARY_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.37,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), new ArrayList<>());
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), Collections.singletonList(anchorNew));
            } else if (StringUtils.equals(resourcePattern, Constant.JUNIOR_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.3,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), new ArrayList<>());
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), Collections.singletonList(anchorNew));
            } else if (StringUtils.equals(resourcePattern, Constant.JUNIOR_CHEMISTRY_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), new ArrayList<>());
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), Collections.singletonList(anchorNew));
            } else if (StringUtils.equals(resourcePattern, Constant.HIGH_MATH_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), new ArrayList<>());
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), Collections.singletonList(anchorNew));
            } else if (StringUtils.equals(resourcePattern, Constant.HIGH_CHEMISTRY_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), new ArrayList<>());
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), Collections.singletonList(anchorNew));
            } else if (StringUtils.equals(resourcePattern, Constant.HIGH_PHYSICS_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), new ArrayList<>());
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), Collections.singletonList(anchorNew));
            } else if (StringUtils.equals(resourcePattern, Constant.JUNIOR_BIOLOGY_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), new ArrayList<>());
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), Collections.singletonList(anchorNew));
            } else if (StringUtils.equals(resourcePattern, Constant.HIGH_BIOLOGY_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), new ArrayList<>());
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), Collections.singletonList(anchorNew));
            } else if (StringUtils.equals(resourcePattern, Constant.PRIMARY_SCIENCE_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), new ArrayList<>());
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), Collections.singletonList(anchorNew));
            } else if (StringUtils.equals(resourcePattern, Constant.JUNIOR_SCIENCE_PATTERN)) {
                String anchorNew = refuseRecNorm(topicsAnchorsScoreMap.get(mapEntry.getKey()), mapEntry.getValue().get(anchor), resourcePattern, anchor, 1, 0.1f, 0.8f);
                if (anchorNew.isEmpty()) {
                    log.info("【锚点预测method-投票模块】学科学段{},该题:{} ,最高概率锚点softmax低于0.2,拒绝识别,返回空字符串", resourcePattern, mapEntry.getKey());
                    topicAnchorResult.put(mapEntry.getKey(), new ArrayList<>());
                    continue;
                }
                //去除 数据魔法值
                topicAnchorResult.put(mapEntry.getKey(), Collections.singletonList(anchorNew));
            } else {
                log.error("【锚点预测method-投票模块】无此学科学段{}", resourcePattern);
            }

        }
        log.info("【锚点预测method-投票模块】最终投票结果：{}", topicAnchorResult);
        return topicAnchorResult;
    }



    public static Map<String, Map<String, Float>> anchorFilterBySpv(AnchorPredictParams anchorPredictParams, Map<String, Map<String, Float>> topicsAnchorsScoreMap, String resourcePattern) {
        //当前支持锚点过滤+按教材版本过滤
        List<String> filtedAnchors = new ArrayList<>();
        for (Map.Entry<String, Map<String, Float>> topicEntry : topicsAnchorsScoreMap.entrySet()) {
            for (Map.Entry<String, Float> anchorEntry : topicEntry.getValue().entrySet()) {
                if (StringUtils.equals(Objects.requireNonNull(FileReaderUtil.of(resourcePattern)).getAnchorIdToBookversion().get(anchorEntry.getKey()), anchorPredictParams.getUserInfo().getBookCode())) {
                    filtedAnchors.add(anchorEntry.getKey());
                }
            }
            break;
        }
//        log.debug("【锚点预测method】按教材版本过滤后锚点列表：{},Size:{}", filtedAnchors, filtedAnchors.size());
        Set<String> resultSet = new HashSet<>();
        Map<String, String> map = new HashMap<>();
        List<String> supportAnchors = anchorPredictParams.getSupportAnchors() != null ? anchorPredictParams.getSupportAnchors() : new ArrayList<>();
        new HashSet<>(supportAnchors).forEach(i2 -> {
            map.put(i2 + "", i2);
        });
        filtedAnchors.forEach(i1 -> {
            String m = map.get(i1.substring(Constant.POINT_MAGICNUM) + "");
            if (m != null) {
                resultSet.add(i1);
            }
        });

//        log.debug("【锚点预测method】按支持锚点过滤后锚点列表：{},Size:{}", resultSet, resultSet.size());
        Map<String, Map<String, Float>> anchorFilter = new HashMap<>();
        for (Map.Entry<String, Map<String, Float>> topicEntry : topicsAnchorsScoreMap.entrySet()) {
            Map<String, Float> anchorsMap = new HashMap<>();
            anchorFilter.put(topicEntry.getKey(), anchorsMap);
            Map<String, Float> anchorEntrys = topicEntry.getValue();
            for (String s : resultSet) {
                anchorsMap.put(s, anchorEntrys.get(s));
            }
        }
        return anchorFilter;
    }

    public static Map<String, Map<String, Float>> anchorFilterBySpvTopN(AnchorPredictParams anchorPredictParams, Map<String, Map<String, Float>> topicsAnchorsScoreMap, String resourcePattern) {
        //当前支持锚点过滤+按教材版本过滤
        List<String> filtedAnchors = new ArrayList<>();
        for (Map.Entry<String, Map<String, Float>> topicEntry : topicsAnchorsScoreMap.entrySet()) {
            for (Map.Entry<String, Float> anchorEntry : topicEntry.getValue().entrySet()) {
                if (StringUtils.equals(Objects.requireNonNull(FileReaderUtil.of(resourcePattern)).getAnchorIdToBookversion().get(anchorEntry.getKey()), anchorPredictParams.getUserInfo().getBookCode())) {
                    filtedAnchors.add(anchorEntry.getKey());
                }
            }
            break;
        }
//        log.debug("【锚点预测method】按教材版本过滤后锚点列表：{},Size:{}", filtedAnchors, filtedAnchors.size());
        Set<String> resultSetByBv = new HashSet<>(filtedAnchors);
        Set<String> resultSetByBvsp = new HashSet<>();
        Map<String, String> map = new HashMap<>();
        List<String> supportAnchors = anchorPredictParams.getSupportAnchors() != null ? anchorPredictParams.getSupportAnchors() : new ArrayList<>();
        new HashSet<>(supportAnchors).forEach(i2 -> {
            map.put(i2 + "", i2);
        });
        filtedAnchors.forEach(i1 -> {
            String m = map.get(i1.substring(Constant.POINT_MAGICNUM) + "");
            if (m != null) {
                resultSetByBvsp.add(i1);
            }
        });

//        log.debug("【锚点预测method】按支持锚点过滤后锚点列表：{},Size:{}", resultSet, resultSet.size());
        Map<String, Map<String, Float>> anchorFilter = new HashMap<>();
        if(!resultSetByBvsp.isEmpty()){
            log.info("按照教材版本+支持列表过滤！");
            filterMethod(anchorFilter,topicsAnchorsScoreMap,resultSetByBvsp);
        }else if(!resultSetByBv.isEmpty()){
            log.info("按照教材版本过滤！");
            filterMethod(anchorFilter,topicsAnchorsScoreMap,resultSetByBv);
        }else {
            log.info("未被教材版本+支持列表过滤！");
            return topicsAnchorsScoreMap;
        }
        return anchorFilter;
    }

    private static void filterMethod(Map<String, Map<String, Float>> anchorFilter, Map<String, Map<String, Float>> topicsAnchorsScoreMap,Set<String> resultSet){
        for (Map.Entry<String, Map<String, Float>> topicEntry : topicsAnchorsScoreMap.entrySet()) {
            Map<String, Float> anchorsMap = new HashMap<>();
            anchorFilter.put(topicEntry.getKey(), anchorsMap);
            Map<String, Float> anchorEntrys = topicEntry.getValue();
            for (String s : resultSet) {
                anchorsMap.put(s, anchorEntrys.get(s));
            }
        }
    }

    public static StringBuffer patternBody(String topicBody) {
        //去除题号和得分  保留中间题干
        Matcher m = pBody.matcher(topicBody);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            m.appendReplacement(sb, "");
        }
        m.appendTail(sb);
        return sb;
    }

    public static StringBuffer patternNumToWorld(StringBuffer topicBody) {
        //整数和小数转为汉字
        Matcher m = pNum.matcher(topicBody);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            if (m.group().length() < 239) {
                m.appendReplacement(sb, DigitalToChineseUtil.bigDecimal2chineseNum(BigDecimal.valueOf(Double.parseDouble(m.group()))));
            }
        }
        m.appendTail(sb);
        return sb;
    }

    public static StringBuffer patternNumToWorldPrimary(StringBuffer topicBody) {
        //整数和小数转为汉字
        Matcher m = pNum.matcher(topicBody);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            if (m.group().length() < 239) {
                m.appendReplacement(sb, DigitalToChineseUtil.bigDecimal2chineseNumPrimary(BigDecimal.valueOf(Double.parseDouble(m.group()))));
            }
        }
        m.appendTail(sb);
        return sb;
    }

    public static StringBuffer patternNumToWorldPhysics(String topicBody) {
        //整数和小数转为汉字
        Matcher m = pNumPhysics.matcher(topicBody);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            if (m.group().length() < 239) {
                String numStr = m.group();
                BigDecimal bd;
                if (numStr.contains(".")) {
                    bd = BigDecimal.valueOf(Double.parseDouble(numStr));
                } else {
                    if (numStr.length() <= 12) {
                        bd = BigDecimal.valueOf(Long.parseLong(numStr));
                    } else {
                        bd = new BigDecimal(numStr);
                    }

                }
                m.appendReplacement(sb, DigitalToChineseUtil.bigDecimal2chineseNumPhysics(bd));
            }
        }
        m.appendTail(sb);
        return sb;
    }

    public static String patternOperatorToWorld(StringBuffer topicBody) {
        //+-×÷=映射为汉字：加减乘除等于
        Matcher m = pOperator.matcher(topicBody);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            if (OPERATOR.contains(m.group())) {
                m.appendReplacement(sb, OPERATOR_CH.get(OPERATOR.indexOf(m.group())));
            }
        }
        m.appendTail(sb);
        return sb.toString().replace("\n", "");
    }

    public static String patternOperatorToWorldPhysics(StringBuffer topicBody) {
        //+-×÷=映射为汉字：加减乘除等于
        Matcher m = pOperatorPhysics.matcher(topicBody);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            if (OPERATOR.contains(m.group())) {
                m.appendReplacement(sb, OPERATOR_CH.get(OPERATOR.indexOf(m.group())));
            }
        }
        m.appendTail(sb);
        return sb.toString().replace("\n", "");
    }

    public static String patternFinalProcess(String input) {
        //后处理规则@梅阳阳yymei2
        Matcher m2 = p2.matcher(input);
        if (m2.find()) {
            input = m2.replaceAll(m2.group(1));
        }
        Matcher m3 = p3.matcher(input);
        if (m3.find()) {
            String blank = "";
            input = m3.replaceAll(blank);
        }
        Matcher m5 = p5.matcher(input);
        if (m5.find()) {
            String blank2 = "  ";
            input = m5.replaceAll(blank2);
        }
        input = input.replaceAll("gt", ">");
        input = input.replaceAll("lt", "<");
//        input = input.replaceAll("[\\\\,.，。\\n：（）〈〉〔〕【】；·∙● 、！？“”…⋯?]|ding", "");
//        input = input.replaceAll("[：（）〈〉〔〕【】；·∙● 、！？“”…⋯\\\\,.，．。\n\";_]|ding", "");
        input = input.replaceAll("[．\\\\,.，。\n：（）〈〉〔〕【】；·∙● 、！？“”…⋯]|ding", "");
        input = input.replaceAll("amp;|\\uD835\\uDEE5", "");
//        input = input.replaceAll("∘", "度");
        input = input.replaceAll("<!.*?>", "");
        //修改{{}}为{}
        input = input.replaceAll("\\{(.*?)\\}", "$1");
        return input;

    }

    public static String patternFinalProcessPrimary(String input) {
        //后处理规则@梅阳阳yymei2
        Matcher m2 = p2.matcher(input);
        if (m2.find()) {
            input = m2.replaceAll(m2.group(1));
        }
        Matcher m3 = p3.matcher(input);
        if (m3.find()) {
            String blank = "";
            input = m3.replaceAll(blank);
        }
        Matcher m5 = p5.matcher(input);
        if (m5.find()) {
            String blank2 = "  ";
            input = m5.replaceAll(blank2);
        }
        input = input.replaceAll("gt", ">");
        input = input.replaceAll("lt", "<");
        input = input.replaceAll("[\"“”!！?？;：；_\\\\,\n.，。 、·∙●…⋯（）〈〉〔〕【】]", "");
        input = input.replaceAll("amp;|\\uD835\\uDEE5", "");
        //修改{{}}为{}
        input = input.replaceAll("\\{(.*?)\\}", "$1");
        input = input.replaceAll("\\^∘", "度");
        input = input.replaceAll("∘", "度");
        input = input.replaceAll("<!.*?>", "");
        return input;

    }

    public static List<String> patternSemester(String fullPageSearchRes) {
        //正则匹配学期
        List<String> list = new ArrayList<>();
        if (fullPageSearchRes == null) {
            return list;
        }
        Matcher m = pSemester.matcher(fullPageSearchRes);
        while (m.find()) {
            list.add(m.group());
        }
        return list;
    }

    public static void semesterOrChapterFilter(Map<String, Map<String, Float>> anchorFilter, Map<String, String> anchorIdToSemesterOrChapter, String semesterOrChapter) {
        //按照学期或章节过滤
        for (Map.Entry<String, Map<String, Float>> topicEntry : anchorFilter.entrySet()) {
            Map<String, Float> anchorsMap = topicEntry.getValue();
            Map<String, Float> anchorsMapCopy = new HashMap<>(anchorsMap);

            if (anchorsMap.isEmpty()) {
                log.info("【锚点预测method-过滤模块】按照学期或章节:{} 过滤,当前试题index:{},对应锚点长度为0", semesterOrChapter, topicEntry.getKey());
                continue;
            }
            for (Map.Entry<String, Float> anchorEntry : anchorsMapCopy.entrySet()) {
                if (!StringUtils.equals(anchorIdToSemesterOrChapter.get(anchorEntry.getKey()), semesterOrChapter)) {
                    anchorsMap.remove(anchorEntry.getKey());
                }
            }
        }
    }

    private static boolean refuseRec(Map<String, Float> topicAnchorsScoreMap, Float anchorScore, Double threshold) {
        Map<Float, String> anchors = new HashMap<>();
//        Map<String,Float > anchors = new HashMap<>(); 老的实现方式
        for (Map.Entry<String, Float> entry : topicAnchorsScoreMap.entrySet()) {
            //锚点复制后，存在相同得分锚点被拒识，当前对得分一样的锚点进行过滤，前提是锚点得分唯一
            anchors.put(entry.getValue(), entry.getKey().substring(Constant.POINT_MAGICNUM));
        }
//        log.debug("分母,参与拒识的锚点：{}", anchors);
        double[] softDeno = new double[anchors.size()];
        int i = 0;
        for (Map.Entry<Float, String> entry : anchors.entrySet()) {
            softDeno[i] = entry.getKey();
            i++;
        }
        double softMax = BertFullTokenizer.softmax(softDeno, anchorScore);
        log.info("【拒识模块】softMax={}", softMax);
        return softMax < threshold;
    }

    private static String refuseRecNorm(Map<String, Float> topicAnchorsScoreMap, Float anchorScore, String resourcePattern, String anchor, Integer logitRate, Float thresholdDown, Float thresholdUp) {
        Map<Float, String> anchors = new HashMap<>();
//        Map<String,Float > anchors = new HashMap<>(); 老的实现方式
        for (Map.Entry<String, Float> entry : topicAnchorsScoreMap.entrySet()) {
            //锚点复制后，存在相同得分锚点被拒识，当前对得分一样的锚点进行过滤，前提是锚点得分唯一
            anchors.put(entry.getValue(), entry.getKey());
        }
//        log.debug("分母,参与拒识的锚点：{}", anchors);
        double[] softDeno = new double[anchors.size()];
        int i = 0;
        for (Map.Entry<Float, String> entry : anchors.entrySet()) {
            softDeno[i] = entry.getKey() * logitRate;
            i++;
        }
        anchorScore = anchorScore * logitRate;
        double softMax = BertFullTokenizer.softmax(softDeno, anchorScore);
        log.info("【拒识模块】softMax={}", softMax);

        String anchorNew = anchor.substring(Constant.POINT_MAGICNUM);
        if (softMax < thresholdDown) {
            anchorNew = "";
        } else if (softMax < thresholdUp) {
//            String anchorName = FileReaderUtilJuniorPhsics.of().getAnchorIdToName().get(anchor);
            String anchorName = FileReaderUtil.of(resourcePattern).getAnchorIdToName().get(anchor);
            if (anchorName.contains("-") || anchorName.contains("(") || anchorName.contains("（")) {
                String[] parts = anchorName.split("-|\\(|（", 2);
                String anchorMainName = parts[0];
                TreeMap<Float, String> sortedMap = new TreeMap<>(Collections.reverseOrder());
                sortedMap.putAll(anchors);
                // 输出排序后的Map
                int j = 0;
                for (Map.Entry<Float, String> entry : sortedMap.entrySet()) {
                    if (j >= 10) {
                        break;
                    }
//                    if(FileReaderUtilJuniorPhsics.of().getAnchorIdToName().get(entry.getValue()).equals(anchorMainName)){
                    if (FileReaderUtil.of(resourcePattern).getAnchorIdToName().get(entry.getValue()).equals(anchorMainName)) {
                        anchorNew = entry.getValue().substring(Constant.POINT_MAGICNUM);
                        log.info("【拒识模块】匹配到主锚点={}", anchorNew);
                        break;
                    }
                    j++;
                }
            }
        }
        return anchorNew;
    }

    public static Map<String, Map<String, Float>> getMaxScoreAnchor(Map<String, Map<String, Float>> topicsToAnchors, AnchorPredictParams anchorPredictParams) {
        Map<String, Map<String, Float>> mapMap = new HashMap<>();
        for (Map.Entry<String, Map<String, Float>> mapEntry : topicsToAnchors.entrySet()) {
            Map<String, Float> anchorMap = new HashMap<>();
            //兜底-防止题目锚点全部被过滤掉  兜底为空字符串并将得分置为1.0
            if (mapEntry.getValue().isEmpty()) {
                anchorMap.put("", 1.0f);
                mapMap.put(mapEntry.getKey(), anchorMap);
                log.info("【锚点预测method-过滤模块】topic:{} 当前锚点被教材版本和支持锚点列表全部过滤掉 锚点为空字符串并赋概率值1.0", mapEntry.getKey());
                continue;
            }
            String maxScoreAnchor = mapEntry.getValue().entrySet().iterator().next().getKey();
            for (Map.Entry<String, Float> entry : mapEntry.getValue().entrySet()) {
                //相同锚点对比前缀大小，取前缀大的锚点
                if (entry.getValue() >= mapEntry.getValue().get(maxScoreAnchor)) {
                    if (entry.getKey().substring(Constant.POINT_MAGICNUM).equals(maxScoreAnchor.substring(Constant.POINT_MAGICNUM))) {
                        if (entry.getKey().compareTo(maxScoreAnchor) > 0) {
                            maxScoreAnchor = entry.getKey();
                            continue;
                        } else {
                            continue;
                        }
                    }
                    maxScoreAnchor = entry.getKey();
                }
            }

            anchorMap.put(maxScoreAnchor, mapEntry.getValue().get(maxScoreAnchor));
            log.info("【锚点预测method-过滤模块】获取topic:{} 当前概率最高锚点:{},得分:{}", mapEntry.getKey(), maxScoreAnchor, mapEntry.getValue().get(maxScoreAnchor));
            mapMap.put(mapEntry.getKey(), anchorMap);
        }
        return mapMap;
    }

    public static Map<String, Map<String, Float>> getMaxScoreAnchorTopN(Map<String, Map<String, Float>> topicsToAnchors, AnchorPredictParams anchorPredictParams) {
        Map<String, Map<String, Float>> mapMap = new HashMap<>();
        for (Map.Entry<String, Map<String, Float>> mapEntry : topicsToAnchors.entrySet()) {
            Map<String, Float> anchorMap = new LinkedHashMap<>();
            //兜底-防止题目锚点全部被过滤掉  兜底为空字符串并将得分置为1.0
            if (mapEntry.getValue().isEmpty()) {
                anchorMap.put("", 1.0f);
                mapMap.put(mapEntry.getKey(), anchorMap);
                log.info("【锚点预测method-过滤模块】topic:{} 当前锚点被教材版本和支持锚点列表全部过滤掉 锚点为空字符串并赋概率值1.0", mapEntry.getKey());
                continue;
            }
            Map<String, Float> orderCands = Operators.sortByValue(mapEntry.getValue());
            Map<Float, List<String>> topScore2Anchors = new LinkedHashMap<>();
            for (Map.Entry<String, Float> entry : orderCands.entrySet()) {
                if (topScore2Anchors.size() >= anchorPredictParams.getTopN()) {
                    break;
                }
                topScore2Anchors.computeIfAbsent(entry.getValue(), k -> new ArrayList<>()).add(entry.getKey());
            }
//            log.info("试题id={},锚点预测top3得分&对应锚点列表={}", mapEntry.getKey(), JSON.toJSONString(topScore2Anchors));
            String anchorNow = anchorMagic;
            for (Map.Entry<Float, List<String>> entry : topScore2Anchors.entrySet()) {
                if (anchorMap.size() >= anchorPredictParams.getTopN()) {
                    break;
                }
                List<String> values = entry.getValue();
                values.sort(Comparator.nullsLast(Comparator.reverseOrder()));
                for (String anchor : entry.getValue()) {
                    if (!StringUtils.equals(anchorNow.substring(Constant.POINT_MAGICNUM), anchor.substring(Constant.POINT_MAGICNUM)) && anchorMap.size() < anchorPredictParams.getTopN()) {

                        anchorMap.put(anchor.substring(Constant.POINT_MAGICNUM), mapEntry.getValue().get(anchor));
                    }
                    if(anchorMap.size() >= anchorPredictParams.getTopN()){
                        break;
                    }
                    anchorNow = anchor;
                }
            }
//            log.info("试题id={},锚点预测top3得分&对应锚点取其一={}", mapEntry.getKey(), JSON.toJSONString(anchorMap));

            mapMap.put(mapEntry.getKey(), anchorMap);
        }
        return mapMap;
    }

    public static Map<String, Boolean> semesterOrChaperVerify(Map<String, Map<String, Float>> anchorFilterMax, Map<String, String> anchorIdToSemesterOrChapter, String semester) {
        List<String> semesterOrChaperList = new ArrayList<>();

        for (Map.Entry<String, Map<String, Float>> mapEntry : anchorFilterMax.entrySet()) {
            String anchor = mapEntry.getValue().entrySet().iterator().next().getKey();
            semesterOrChaperList.add(anchorIdToSemesterOrChapter.get(anchor));
//            log.debug("锚点:{},章节:{}", anchor, anchorIdToSemesterOrChapter.get(anchor));
        }
        Map<String, Integer> semesterOrChapterStatistic = new HashMap<>();
        for (String s : semesterOrChaperList) {
            semesterOrChapterStatistic.merge(s, 1, Integer::sum);
        }
        List<Map.Entry<String, Integer>> semesterOrChapterEntryList = new ArrayList<>(semesterOrChapterStatistic.entrySet());
        semesterOrChapterEntryList.sort((o1, o2) -> {
            //倒序排列，正序反过来
            return o2.getValue() - o1.getValue();
        });
        log.info("按章节数量排序后:{}", semesterOrChapterEntryList);
        Map<String, Boolean> res = new HashMap<>();
        for (Map.Entry<String, Integer> entry : semesterOrChapterEntryList) {
            if (entry.getValue() == 1 || entry.getKey() == null) {
                res.put(entry.getKey(), false);
            }
            //>=50%题目学期(当前概率最高锚点学期作为题目学期)为同一个学期 && =输入学期
            else if (entry.getValue() * 2 >= anchorFilterMax.size() && StringUtils.equals(entry.getKey(), semester)) {
                res.put(entry.getKey(), true);
            }
            //题目数量>=3  &&  80%以上题目在同一章（概率最高锚点对应章节作为题目章节）
            else if (StringUtils.equals("chapterVerify", semester) && anchorFilterMax.size() >= 3 && entry.getValue() * 5 >= anchorFilterMax.size() * 4) {
                res.put(entry.getKey(), true);
            }
            if (res.isEmpty()) {
                res.put("null", false);
            }
            return res;
        }
        res.put("null", false);
        return res;
    }
}