package com.iflytek.rec.anchorpredic.impl;

import com.iflytek.rec.anchorpredic.interfaces.ILatexJsonAnalys;
//import com.iflytek.rec.anchorpredic.method.Latex2Mathml;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/10/19 15:51
 */
public class LatexJsonAnalysImpl implements ILatexJsonAnalys {
    private static final Logger log = LoggerFactory.getLogger(LatexJsonAnalysImpl.class);
    private static final String PATTERN = "ifly-latex-begin*ifly-latex-end";

    private static final Pattern p = Pattern.compile(PATTERN);
    @Override
    public String latexJsonUseMap(String ocrText) {
        return AnchorPredicMethodImpl.dataAnalysisOcr<PERSON><PERSON>(ocrText);
    }

}
