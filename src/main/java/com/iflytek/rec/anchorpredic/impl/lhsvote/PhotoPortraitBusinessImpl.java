package com.iflytek.rec.anchorpredic.impl.lhsvote;

import com.iflytek.rec.anchorpredic.interfaces.lhsvote.PhotoPortraitBusiness;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.PhotoTopicOutput;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.PointRecgRequest;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.PointRecgResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class PhotoPortraitBusinessImpl implements PhotoPortraitBusiness {
    /**
     * logger
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(PhotoPortraitBusinessImpl.class);
    /**
     * 锚点预测业务实现类
     */
//        @Resource
    private final static AnchorPredictBusinessImpl anchorPredictBusiness = new AnchorPredictBusinessImpl();


    @Override
    public PointRecgResponse pointRecg(PointRecgRequest request) {
        //获取锚点预测结果
        long startPret = System.currentTimeMillis();
        List<PhotoTopicOutput> photoTopicOutputList = anchorPredictBusiness.offlinePredict(request);
        LOGGER.info("入参试题个数:{},点识别结果个数:{},点识别总耗时:{}ms", request.getPhotoTopicInputList().size(),
                photoTopicOutputList.size(), System.currentTimeMillis() - startPret);

//            //字段不传时默认保存作答日志
//            if (request.getSaveStudentLog() == null || request.getSaveStudentLog()) {
//                learningSituationReport(request, photoTopicOutputList, SourceType.PHOTO_INPUT);
//            }
        //构建返回结果
        PointRecgResponse response = new PointRecgResponse();
        response.setPhotoId(request.getPhotoId());
        response.setUserId(request.getUserId());
        response.setBizCode(request.getBizCode());
        response.setSubjectCode(request.getSubjectCode());
        response.setPhaseCode(request.getPhaseCode());
        response.setPhotoTopicOutputList(photoTopicOutputList);

        return response;
    }
}
