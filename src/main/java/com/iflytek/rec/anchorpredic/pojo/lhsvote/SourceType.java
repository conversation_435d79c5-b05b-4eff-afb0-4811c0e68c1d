package com.iflytek.rec.anchorpredic.pojo.lhsvote;

import java.awt.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class SourceType implements Serializable {

    private static final long serialVersionUID = 1L;

    private SourceType()
    {
        throw new IllegalStateException("Utility class");
    }
    /**
     * 同步场景-测评（锚点推荐）
     */
    public static final String SYNC_EVAL = "syncEval";

    /**
     * 同步场景-推荐提升（锚点推荐）
     */
    public static final String SYNC_REC = "syncRec";

    /**
     * 同步场景-针对学（锚点推荐）
     */
    public static final String SYNC_AIM = "syncAim";

    /**
     * 备考场景-阶段备考--互动题（考点推荐）
     */
    public static final String EXAM_STAGE_INTER = "examStageInter";

    /**
     * 备考场景-阶段备考--变式题（考点推荐）
     */
    public static final String EXAM_STAGE_VARY = "examStageVary";

    /**
     * 备考场景-阶段备考--反馈测试题（考点推荐）
     */
    public static final String EXAM_STAGE_BACK = "examStageBack";

    /**
     * 备考场景-单元备考--摸底测（考点推荐）
     */
    public static final String EXAM_UNIT_BOTTOM = "examUnitBottom";

    /**
     * 备考场景-单元备考--互动题（考点推荐）
     */
    public static final String EXAM_UNIT_INTER = "examUnitInter";

    /**
     * 备考场景-单元备考--变式题（考点推荐）
     */
    public static final String EXAM_UNIT_VARY = "examUnitVary";

    /**
     * 备考场景-单元备考--反馈测试题（考点推荐）
     */
    public static final String EXAM_UNIT_BACK = "examUnitBack";

    /**
     * 拍搜画像-拍搜画像入参试题
     */
    public static final String PHOTO_INPUT = "photoInput";

    /**
     * BC端融合学情上报
     */
    public static final String BC_INPUT = "bcInput";
    /**
     * 课后三点半-拍照测评（锚点推荐）
     */
    public static final String EVALUATION_BY_PHOTO_RESULT = "evaluationByPhotoResult";
    /**
     * ai诊断
     */
    public static final String AI_DIAGNOSIS="aiDiagnosis";

    /**
     * 获取阶段备考sour类型
     * @return  阶段备考source
     */
    public static java.util.List<String> getStageExamSource(){
        java.util.List<String> sourceList = new ArrayList<>();
        sourceList.add(EXAM_STAGE_INTER);
        sourceList.add(EXAM_STAGE_VARY);
        sourceList.add(EXAM_STAGE_BACK);

        return sourceList;
    }

    /**
     * 获取锚点sour类型
     * @return  阶段备考source
     */
    public static java.util.List<String> getAnchorSource(){
        java.util.List<String> sourceList = new ArrayList<>();
        sourceList.add(SYNC_EVAL);
        sourceList.add(SYNC_AIM);
        sourceList.add(SYNC_REC);

        return sourceList;
    }

    /**
     * 获取单元备考sour类型
     * @return  单元备考source
     */
    public static java.util.List<String> getUnitExamSource(){
        java.util.List<String> sourceList = new ArrayList<>();
        sourceList.add(EXAM_UNIT_BOTTOM);
        sourceList.add(EXAM_UNIT_INTER);
        sourceList.add(EXAM_UNIT_VARY);
        sourceList.add(EXAM_UNIT_BACK);

        return sourceList;
    }

    /**
     * 获取其他source类型（非正常引擎推荐）
     * @return  其他类型日志来源
     */
    public static java.util.List<String> getOtherSource(){
        List<String> sourceList = new ArrayList<>();
        sourceList.add(PHOTO_INPUT);
        sourceList.add(EVALUATION_BY_PHOTO_RESULT);
        sourceList.add(BC_INPUT);
        sourceList.add(AI_DIAGNOSIS);

        return sourceList;
    }

}
