package com.iflytek.rec.anchorpredic.pojo.lhsvote;

import com.iflytek.rec.anchorpredic.expand.CollectionElementNotNull;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

@Data
public class PointPortraitRequest implements Serializable {
    /**
     * 序列号
     */
    private static final long serialVersionUID = -296350162104616066L;
    /**
     * 用户名
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    /**
     * 业务码 学习机：zsy_xxj 内部测试：adaptive_test
     */
    @NotBlank(message = "业务码不能为空")
    @Pattern(regexp = "zsy_xxj|adaptive_test|zsy_byod", message = "业务码不正确")
    private String bizCode;
    /**
     * 学科
     */
    @NotBlank(message = "学科不能为空")
    @Pattern(regexp = "^[0]{1}[256]{1}$|19|13", message = "学科不能为02、05、06、19、13以外的值")
    private String subjectCode;
    /**
     * 学段
     */
    @NotBlank(message = "学段不能为空")
    @Pattern(regexp = "^[0]{1}[3-5]{1}$", message = "学段不能为03、04、05以外的值")
    private String phaseCode;
    /**
     * 年级code
     */
    @NotBlank(message = "年级不能为空")
    private String gradeCode;
    /**
     * 教材版本
     */
    @NotBlank(message = "教材版本不能为空")
    private String bookVersionCode;
    /**
     * 书本code
     */
    private String bookCode;
    /**
     * 点的类型（枚举值见PointType）
     */
    @NotBlank(message = "点类型不能为空")
    private String pointType;
    /**
     * 待画像点id
     */
    @NotEmpty(message = "点id不能为空")
    @CollectionElementNotNull(message = "点id不能为null或者空字符串")
    private List<String> pointIdList;
    /**
     * 薄弱点推荐标识-标志是否在画像同时推荐薄弱点
     * true：推荐薄弱点   false：不推荐薄弱点
     */
    @NotNull(message = "薄弱点推荐标识不能为空")
    private Boolean weakPointRecommend = false;

    /**
     * 场景信息：包含区域code等
     */
    private Scene scene;

    private Integer evaluNum;

    private String examType;
}
