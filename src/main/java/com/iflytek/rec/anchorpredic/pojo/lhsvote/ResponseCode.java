package com.iflytek.rec.anchorpredic.pojo.lhsvote;

import java.util.HashMap;
import java.util.Map;

public class ResponseCode {

    /**
     * 系统处理正常
     */
    public static final int SUCCESS_HEAD = 0;

    /**
     * 系统处理未知异常
     */
    public static final int EXCEPTION_HEAD = 1;

    /**
     * 调用英语引擎失败
     */
    public static final int CALL_ENGINE_ERROR = 2;

    /**
     * 入参校验失败
     */
    public static final int PARAM_VALIDATION_ERROR = 3;


    public static final Map<Integer, String> RESP_INFO = new HashMap<Integer, String>();

    static {
        // 异常消息 相关
        RESP_INFO.put(SUCCESS_HEAD, "系统处理正常");
        RESP_INFO.put(EXCEPTION_HEAD, "系统处理未知异常");
        RESP_INFO.put(CALL_ENGINE_ERROR, "服务内部调用英语引擎失败");
        RESP_INFO.put(PARAM_VALIDATION_ERROR, "请求入参校验失败");
    }
}
