package com.iflytek.rec.anchorpredic.pojo.lhsvote;

import java.util.Arrays;
import java.util.List;

public class PhaseCode {
    private PhaseCode()
    {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 小学学段编码
     */
    public static final String PRIMARY = "03";
    /**
     * 初中学段编码
     */
    public static final String JUNIOR = "04";
    /**
     * 高中学段编码
     */
    public static final String SENIOR = "05";
    /**
     * 所有学段列表（不可修改列表）
     */
    public static final List<String> PHASE_LIST = Arrays.asList(PRIMARY, JUNIOR, SENIOR);
}
