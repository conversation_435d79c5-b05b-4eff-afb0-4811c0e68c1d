package com.iflytek.rec.anchorpredic.pojo.lhsvote;

import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;

public class GradeCode {
    private GradeCode()
    {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 一年级
     */
    public static final String FIRST_GRADE = "01";
    /**
     * 二年级
     */
    public static final String SECOND_GRADE = "02";
    /**
     * 三年级
     */
    public static final String THIRD_GRADE = "03";
    /**
     * 四年级
     */
    public static final String FOURTH_GRADE = "04";
    /**
     * 五年级
     */
    public static final String FIFTH_GRADE = "05";
    /**
     * 六年级
     */
    public static final String SIXTH_GRADE = "06";
    /**
     * 七年级
     */
    public static final String SEVENTH_GRADE = "07";
    /**
     * 八年级
     */
    public static final String EIGHTH_GRADE = "08";
    /**
     * 九年级
     */
    public static final String NINTH_GRADE = "09";
    /**
     * 高一
     */
    public static final String TENTH_GRADE = "10";
    /**
     * 高二
     */
    public static final String ELEVENTH_GRADE = "11";
    /**
     * 高三
     */
    public static final String TWELFTH_GRADE = "12";
    /**
     * 高中
     */
    private static final String NINETEEN_GRADE = "19";
    /**
     * 小学年级列表
     */
    private static List<String> primaryGradeList = Arrays.asList(FIRST_GRADE, SECOND_GRADE, THIRD_GRADE,
            FOURTH_GRADE, FIFTH_GRADE, SIXTH_GRADE);
    /**
     * 初中年级列表
     */
    private static List<String> juniorGradeList = Arrays.asList(SIXTH_GRADE, SEVENTH_GRADE, EIGHTH_GRADE, NINTH_GRADE);
    /**
     * 高中年级列表
     */
    private static List<String> seniorGradeList = Arrays.asList(TENTH_GRADE, ELEVENTH_GRADE, TWELFTH_GRADE, NINETEEN_GRADE);

    /**
     * 判断学段code、年级code
     * @param phaseCode     学段code
     * @param gradeCode     年级code
     * @return  true：学段、年级相匹配   false：学段、年级不匹配
     */
    public static boolean validatePhaseGeade(String phaseCode, String gradeCode){
        //学段、年级是否匹配
        boolean legal = false;
        if (StringUtils.isEmpty(phaseCode) || StringUtils.isEmpty(gradeCode)){
            return legal;
        }
        //学段在支持学段范围内
        if (PhaseCode.PHASE_LIST.contains(phaseCode)) {
            switch (phaseCode) {
                case PhaseCode.PRIMARY:
                    legal = primaryGradeList.contains(gradeCode);
                    break;
                case PhaseCode.JUNIOR:
                    legal = juniorGradeList.contains(gradeCode);
                    break;
                case PhaseCode.SENIOR:
                    legal = seniorGradeList.contains(gradeCode);
                    break;
                default:
                    legal = false;
            }
        }

        return legal;
    }
}
