package com.iflytek.rec.anchorpredic.pojo.lhsvote;

import com.iflytek.rec.anchorpredic.expand.CollectionElementNotNull;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

@Data
public class PointRecgRequest implements Serializable {
    /**
     * 序列号
     */
    private static final long serialVersionUID = 5644439481544491751L;
    /**
     * 拍搜图片标识id
     */
    @NotBlank(message = "图片标识id不能为空")
    private String photoId;
    /**
     * 用户Id
     */
    @NotBlank(message = "用户Id不能为空")
    private String userId;
    /**
     * 业务码 学习机：zsy_xxj 内部测试：adaptive_test
     */
    @NotBlank(message = "业务码不能为空")
    @Pattern(regexp = "zsy_xxj|adaptive_test|zsy_byod", message = "业务码不正确")
    private String bizCode;
    /**
     * 学科
     */
    @NotBlank(message = "学科不能为空")
    @Pattern(regexp = "^[0]{1}[256]{1}$", message = "学科不能为02、05、06以外的值")
    private String subjectCode;
    /**
     * 学段
     */
    @NotBlank(message = "学段不能为空")
    @Pattern(regexp = "^[0]{1}[3-5]{1}$", message = "学段不能为03、04、05以外的值")
    private String phaseCode;
    /**
     * 年级code
     */
    @NotBlank(message = "年级不能为空")
    private String gradeCode;
    /**
     * 教材版本
     */
    @NotBlank(message = "教材版本不能为空")
    private String bookVersionCode;
    /**
     * 书本code
     */
    private String bookCode;
    /**
     * 点的类型（枚举值见PointType）
     */
    @NotBlank(message = "点类型不能为空")
    private String pointType;
    /**
     * 拍搜批改传入试题信息
     */
    @Valid
    @NotEmpty(message = "拍搜批改试题信息不能为空")
    @CollectionElementNotNull(message = "拍搜批改试题信息不能为null")
    private List<PhotoTopicInput> photoTopicInputList;

    /**
     * 场景信息：包含区域code等
     */
    private Scene scene;

    /**
     * 是否保存作答日志
     */
    private Boolean saveStudentLog = true;


}
