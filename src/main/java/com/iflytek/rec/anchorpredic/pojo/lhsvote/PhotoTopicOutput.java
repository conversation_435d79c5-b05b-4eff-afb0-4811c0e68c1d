package com.iflytek.rec.anchorpredic.pojo.lhsvote;

import lombok.Data;

@Data
public class PhotoTopicOutput {
    /**
     * 序列号
     */
    private static final long serialVersionUID = 5363358456270641423L;
    /**
     * 试题标识id
     */
    private String id;
    /**
     * 题库试题id
     */
    private String topicId;
    /**
     * 试题难度
     */
    private Integer topicDiff;
    /**
     * 对应点(锚点或考点)id
     */
    private String pointId;
    /**
     * 点类型（见枚举值PointType）
     */
    private String pointType;
    /**
     * 锚/考点对应章节code
     */
    private String catalogCode;
}
