package com.iflytek.rec.anchorpredic.pojo.lhsvote;

public class ValidationMessage {
    private ValidationMessage(){
        throw new IllegalStateException("Constant class");
    }
    /**
     * 入参为null返回异常信息
     */
    public static final String INPUT_NULL_ERROR = "入参不能为null";
    /**
     * 校验点类型接口是否支持（锚点、考点）
     */
    public static final String ILEGAL_POINT_TYPE = "点类型不支持";
    /**
     * 学段与年级不匹配异常信息
     */
    public static final String ILEGAL_PHASE_GRADE = "学段、年级不匹配";
    /**
     * 学科、学段、年级、教材版本、书本code不匹配异常信息
     */
    public static final String ILEGAL_BOOK = "学科、学段、年级、教材版本、书本code不匹配";
    /**
     * 学科、学段、教材版本不匹配异常信息
     */
    public static final String ILEGAL_BOOK_VERSION = "学科、学段、教材版本不匹配";
    /**
     * 学科、学段、年级、教材版本、书本code、点id不匹配异常信息
     */
    public static final String ILEGAL_POINT = "学科、学段、年级、教材版本、书本code、点id不匹配";
    /**
     * 学科、学段、教材版本、点id不匹配异常信息
     */
    public static final String ILEGAL_VERSION_POINT = "学科、学段、教材版本、点id不匹配";
}
