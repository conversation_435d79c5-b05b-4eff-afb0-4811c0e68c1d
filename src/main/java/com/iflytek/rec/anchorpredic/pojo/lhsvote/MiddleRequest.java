package com.iflytek.rec.anchorpredic.pojo.lhsvote;

import com.iflytek.rec.anchorpredic.expand.CollectionElementNotNull;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class MiddleRequest {
    /**
     * 用户Id
     */
    private String userId;

    /**
     * 学科
     */
    private String subjectCode;

    /**
     * 学段
     */
    private String phaseCode;

    /**
     * 年级
     */
    private String gradeCode;

    /**
     * 业务码
     */
    private String bizCode;

    /**
     * 教材版本（人教、沪科等）
     */
    private String bookVersionCode;

    /**
     * 书本code
     */
    private String bookCode;
    @Valid
    @NotEmpty(
            message = "拍搜批改试题信息不能为空"
    )
    @CollectionElementNotNull(
            message = "拍搜批改试题信息不能为null"
    )
    private List<PhotoTopicInput> photoTopicInputList;

    /**
     * 场景信息(目前为区域优推使用)
     */
    private Scene scene;
}
