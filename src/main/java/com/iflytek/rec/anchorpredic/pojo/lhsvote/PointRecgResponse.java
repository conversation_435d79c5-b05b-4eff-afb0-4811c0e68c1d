package com.iflytek.rec.anchorpredic.pojo.lhsvote;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PointRecgResponse implements Serializable {
    /**
     * 序列号
     */
    private static final long serialVersionUID = -3877650413761569020L;
    /**
     * 拍搜图片标识id
     */
    private String photoId;
    /**
     * 用户Id
     */
    private String userId;
    /**
     * 业务码
     */
    private String bizCode;
    /**
     * 学科
     */
    private String subjectCode;
    /**
     * 学段
     */
    private String phaseCode;
    /**
     * 拍搜试题信息识别结果返回
     */
    private List<PhotoTopicOutput> photoTopicOutputList;
}
