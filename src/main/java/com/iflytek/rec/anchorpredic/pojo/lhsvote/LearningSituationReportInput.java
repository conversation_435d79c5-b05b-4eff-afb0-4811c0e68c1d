package com.iflytek.rec.anchorpredic.pojo.lhsvote;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;

@Data
public class LearningSituationReportInput implements Serializable {
    /**
     * 序列号
     */
    private static final long serialVersionUID = -3877650413761569020L;


    /**
     * 拍搜试题标识id
     */
    @NotBlank(message = "试题标识id不能为空")
    private String id;
    /**
     * 得分
     */
    @NotNull(message = "得分不能为空")
    @PositiveOrZero(message = "得分不能为负数")
    private Double score;
    /**
     * 标准分
     */
    @NotNull(message = "标准分不能为空")
    @Positive(message = "标准分不能为零或负数")
    private Double standardScore;

    /**
     * 题库试题id
     */
    @NotBlank(message = "题库试题id不能为空")
    private String topicId;


    /**
     * 对应点(锚点或考点)id
     */
    @NotBlank(message = "对应点(锚点或考点)id不能为空")
    private String pointId;

    /**
     * 点类型（见枚举值PointType）
     */
    @NotBlank(message = "点类型不能为空")
    private String pointType;
    /**
     * 锚/考点对应章节code
     */
    @NotBlank(message = "锚/考点对应章节code不能为空")
    private String catalogCode;
}
