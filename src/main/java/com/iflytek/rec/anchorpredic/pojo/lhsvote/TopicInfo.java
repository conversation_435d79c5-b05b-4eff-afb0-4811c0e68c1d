package com.iflytek.rec.anchorpredic.pojo.lhsvote;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;

@Data
public class TopicInfo implements Serializable {
    /**
     * 序列号
     */
    private static final long serialVersionUID = -6844312518369421270L;

    /**
     * 拍搜题库试题id
     */
    @NotBlank(message = "试题不能为空")
    private String topicId;
    /**
     * 置信度
     */
    @NotNull(message = "置信度不能为空")
    @PositiveOrZero(message = "置信度不能为负数")
    private Double cfdsLevel;
}
