package com.iflytek.rec.anchorpredic.pojo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/10/10 15:04
 */
@Data
public class AnchorPredictParams {
    /**
     * Map<题目Id index,题库文本html>
     */
    @NotNull
    Map<String, String> htmlStrings;
    /**
     * Map<题目Id index,ocr文本>
     */
    @NotNull
    Map<String, String> ocrStrings;

    /**
     * Map<题目Id index,置信度值>
     */
    Map<String, Double> cfdsLevels = new HashMap<>();
    /**
     * 用户信息
     */
    @NotNull
    UserInfo userInfo;
    /**
     * 整页拍搜结果
     */
    String fullPageSearchRes;
    /**
     * 业务支持的锚点列表
     */
    List<String> supportAnchors=new ArrayList<>();
    /**
     * 日志查询 透传
     */
    @NotNull
    String traceId;
    /**
     * 期望返回的topN锚点数，默认返回1个
     */
    int topN = 1;
}