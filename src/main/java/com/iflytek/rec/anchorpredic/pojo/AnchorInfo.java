package com.iflytek.rec.anchorpredic.pojo;

import lombok.Data;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/10/10 15:39
 */

/**{"id": "7771692c-1ec6-437d-bb0e-56a6c580944c", "name": "根据比例的意义判断是否成比例", "subject": 2, "phase": 3, "edition": "8", "edition_name": "浙教版", "book_version": "下册", "grade": 6, "chapter_code": "06020227-001_002", "index": 2760}*/
@Data
public class AnchorInfo {
    /**
     * 锚点id
     */
    String id;
    /**
     * 锚点id对应模型输出得分列表的索引
     */
    int index;

    /**
     * 锚点名称
     */
    String name;
    /**
     * 学科
     */
    int subject;
    /**
     * 年级
     */
    int grade;
    /**
     * 学段
     */
    int phase;
    /**
     * 教材code
     */
    String edition;
    /**
     * 上册/下册
     */
    String book_version;
    /**
     * 教材名称
     */
    String edition_name;
    /**
     * 章节
     */
    String chapter_code;

}