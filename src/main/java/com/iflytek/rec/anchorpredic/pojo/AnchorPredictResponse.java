package com.iflytek.rec.anchorpredic.pojo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/10/10 15:04
 */
@Data
public class AnchorPredictResponse {
    /**
     * 锚点预测结果 Map<试题id index,List<锚点id>> 拒识返回空字符串
     * 正常返回topN结果 根据入参 com.iflytek.rec.anchorpredic.pojo.AnchorPredictParams#topN
     */
    Map<String, List<String>> anchorPredictResult;
}
