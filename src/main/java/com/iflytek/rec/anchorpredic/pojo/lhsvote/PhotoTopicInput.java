package com.iflytek.rec.anchorpredic.pojo.lhsvote;

import com.iflytek.rec.anchorpredic.expand.CollectionElementNotNull;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

@Data
public class PhotoTopicInput implements Serializable {
    /**
     * 序列号
     */
    private static final long serialVersionUID = 5363358456270641423L;
    /**
     * 拍搜试题标识id
     */
    @NotBlank(message = "试题标识id不能为空")
    private String id;
    /**
     * 预测试题信息
     */
    @Valid
    @NotEmpty(message = "拍搜预测试题信息不能为空")
    @CollectionElementNotNull(message = "拍搜预测试题信息不能为null")
    private List<TopicInfo> topicInfos;
    /**
     * OCR结果文本
     */
    private String ocrText;
    /**
     * 得分
     */
    @NotNull(message = "得分不能为空")
    @PositiveOrZero(message = "得分不能为负数")
    private Double score;
    /**
     * 标准分
     */
    @NotNull(message = "标准分不能为空")
    @Positive(message = "标准分不能为零或负数")
    private Double standardScore;

}
