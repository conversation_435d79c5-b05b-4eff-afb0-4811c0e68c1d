package com.iflytek.rec.anchorpredic.utils;

import ai.djl.modality.nlp.DefaultVocabulary;
import com.iflytek.rec.anchorpredic.datafactory.holder.FileReaderUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/10/12 17:15
 */
public class BertFullTokenizerExdFour {
    private static final Logger log = LoggerFactory.getLogger(BertFullTokenizerExdFour.class);

    private static final URL filePathURL;

    private static final ai.djl.modality.nlp.bert.BertFullTokenizer tokenizerENG;
    private static final ai.djl.modality.nlp.bert.BertFullTokenizer tokenizerCH;


    static {
        try {
            filePathURL = FileReaderUtil.getFilePathURL("model/vocab/vocabexdfour.txt");
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
    }

    private static final DefaultVocabulary vocabulary;


    static {
        try {
            vocabulary = initVocabulary();
            tokenizerENG = new ai.djl.modality.nlp.bert.BertFullTokenizer(vocabulary, true);
            tokenizerCH = new ai.djl.modality.nlp.bert.BertFullTokenizer(vocabulary, true);
        } catch (IOException e) {
            log.info("NewBert分词模型加载失败！{}", e.toString());
            throw new RuntimeException(e);
        }
    }

    private static DefaultVocabulary initVocabulary() throws IOException {
        log.info("Bert分词模型加载路径!:{}", filePathURL);
        DefaultVocabulary vocabulary =
                DefaultVocabulary.builder()
                        .optMinFrequency(1)
                        .addFromTextFile(filePathURL)
                        .optUnknownToken("[UNK]")
                        .build();
        log.info("Bert分词模型加载成功!Size:{}", vocabulary.size());
        return vocabulary;
    }

    public static List<String> getBertFullTokenizer(String topicBody, Boolean addSE) {
        topicBody = topicBody.replaceAll("\uD835\uDF0B","π");
        //包含$ 82行会报错
        topicBody = topicBody.replaceAll("\\$","");
        Pattern paBody = Pattern.compile("[\\u4e00-\\u9fa5]+");
        Matcher ma = paBody.matcher(topicBody);
        StringBuffer sbf = new StringBuffer();
        while (ma.find()) {
            ma.appendReplacement(sbf, getTokenizeCh(ma.group(0)));
        }
        ma.appendTail(sbf);

        paBody = Pattern.compile("[^\\u4e00-\\u9fa5|\\\\]+");
        String tt = sbf.toString();
        ma = paBody.matcher(sbf.toString());
        sbf = new StringBuffer();
        while (ma.find()) {
            ma.appendReplacement(sbf, getTokenizeEng(ma.group(0).replaceAll("￥", "")));
        }
        ma.appendTail(sbf);
        List<String> tokensFilter = Arrays.asList(sbf.toString().split("￥"));
        List<String> tokensFilterN = new ArrayList<>();
        for (String s:tokensFilter){
            if(!s.isEmpty()){
                tokensFilterN.add(s);
            }
        }
        if (addSE) {
            tokensFilterN.add("[SEP]");
            tokensFilterN.add(0, "[CLS]");
        }
        log.info("【锚点预测tokenizer】分词结果：{}", tokensFilterN);
        return tokensFilterN;
    }

    public static String getTokenizeEng(String topicBody) {
//        ai.djl.modality.nlp.bert.BertFullTokenizer tokenizer = new ai.djl.modality.nlp.bert.BertFullTokenizer(vocabulary, true);
        List<String> tokens = tokenizerENG.tokenize(topicBody);
        return "￥" + StringUtils.join(tokens, "￥") + "￥";
    }

    public static String getTokenizeCh(String topicBody) {
//        ai.djl.modality.nlp.bert.BertFullTokenizer tokenizer = new ai.djl.modality.nlp.bert.BertFullTokenizer(vocabulary, true);
        List<String> tokens = tokenizerCH.tokenize(topicBody);
        List<String> tokensFilter = new ArrayList<>();
        for (String token : tokens) {
            if (token.length() > 2 && token.contains("##")) {
                tokensFilter.add(token.replace("##", ""));
            } else {
                tokensFilter.add(token);
            }
        }
        return "￥" + StringUtils.join(tokensFilter, "￥") + "￥";
    }

    public static List<Long> getTokensIndex(List<String> tokens) {
        List<Long> tokensIndex = new ArrayList<>();
        for (String token : tokens) {
            tokensIndex.add(vocabulary.getIndex(token));
        }
//        log.info("【锚点预测tokenizer】分词index结果：{}", tokensIndex);

        return tokensIndex;
    }

    public static List<Long> getWordNums(String topicBody, String resourcePattern) {
        //初始化统计词表大小0向量
        List<Long> words = new ArrayList<>();
        while (words.size() != Objects.requireNonNull(FileReaderUtil.of(resourcePattern)).getWordToIndexs().size()) {
            words.add(0L);
        }
        char[] chars = topicBody.toCharArray();
        //统计每个字符出现的次数
        Map<Character, Integer> map = new HashMap();
        for (char c : chars) {
            if (map.containsKey(c)) {
                map.put(c, map.get(c) + 1);
            } else {
                map.put(c, 1);
            }
        }

        for (Map.Entry<Character, Integer> entry : map.entrySet()) {
            String key = String.valueOf(entry.getKey());
            Long value = Long.valueOf(entry.getValue());
            //统计词表不包含置为MAXNUM
            Integer index = Objects.requireNonNull(FileReaderUtil.of(resourcePattern)).getWordToIndexs().getOrDefault(key, Constant.MAXNUM);
            if (index != Constant.MAXNUM) {
                words.set(index, value);
            }
        }
        return words;
    }

    public static List<Long> getMask(List<Long> topicBert) {
        List<Long> masks = new ArrayList<>();
        for (Long l : topicBert) {
            if (l != 0L) {
                masks.add(1L);
            } else {
                masks.add(0L);
            }
        }
        return masks;
    }

    public static double softmax(double[] input, double item) {
        double total = Arrays.stream(input).map(v -> Math.exp(v - item)).sum();
        double output = Math.exp(0) / total;
//        log.debug("【softmax】分子(求e前):{},total:{},result:{}", item, total, output);
        return output;
    }

    public static void main(String[] args) throws IOException {
        ai.djl.modality.nlp.bert.BertFullTokenizer tokenizer = new ai.djl.modality.nlp.bert.BertFullTokenizer(vocabulary, true);
        List<String> tokens = tokenizer.tokenize("直角三角形ABC中∠ACB等于九十&两位数°直线l过点C．当AC等于BC时如图一&一位数分别过点A和B作AD⊥直线l于点DBE⊥直线l于点E．△ACD与△CBE是否全等并说明理由当AC等于八&一位数cmBC等于六&一位数cm时如图二&一位数点B与点F关于直线l对称连接BFCF．点M是AC上一点点N是CF上一点分别过点MN作MD⊥直线l于点DNE⊥直线l于点E点M从A点出发以每秒一&一位数cm的速度沿A→C路径运动终点为C．点N从点F出发以每秒三&一位数cm的速度沿F→C→B→C→F路径运动终点为F．点MN同时开始运动各自达到相应的终点时停止运动设运动时间为t秒．①当△CMN为等腰直角三角形时求t的值②当△MDC与△CEN全等时求t的值");
        System.out.println(tokens);
        System.out.println(getBertFullTokenizer("直角三∘角形ABC中∠ACB等于九十&两位数°直线l过点C．当AC等于BC时如图一&一位数分别过点A和B作AD⊥直线l于点DBE⊥直线l于点E．△ACD与△CBE是否全等并说明理由当AC等于八&一位数cmBC等于六&一位数cm时如图二&一位数点B与点F关于直线l对称连接BFCF．点M是AC上一点点N是CF上一点分别过点MN作MD⊥直线l于点DNE⊥直线l于点E点M从A点出发以每秒一&一位数cm的速度沿A→C路径运动终点为C．点N从点F出发以每秒三&一位数cm的速度沿F→C→B→C→F路径运动终点为F．点MN同时开始运动各自达到相应的终点时停止运动设运动时间为t秒．①当△CMN为等腰直角三角形时求t的值②当△MDC与△CEN全等时求t的值", false));

        //{95d9c987-3a74-4602-b9be-b32d757136e9=7.048729, f53a5984-7bca-4245-bb24-31999cb2fd53=0.7827943,
        // 6ba6d0ef-2e03-4cac-88c2-55a2533d89a8=0.38686466, 5fc432f5-9592-4deb-ac37-4993e71f2bd5=3.472804}

//        double[] li = {0,-1,1,10,-10};
//        double[] li = {7.048729,0.7827943,0.38686466,3.472804};
//        for(double d:li){
//            System.out.println(softmax(li,d));
//        }


//        String s = "如图在Rt\uD835\uDEE5ABC中∠ABC等于九十&两位数^∘∠A等于三十二&两位数^∘点BC在⊙O上边ABAC分别交⊙O于DE两点点B是CD的中点则∠ABE等于_______";
//        String s = "如图在Rt\uD835\uDEE5ABC中∠ABC等于九十&两位数^∘∠A等于三十二&两位数^点BC在⊙O上边ABAC分别交⊙O于DE两点点B是CD的中点则∠ABE等于_______";
//        String s = "如图在RtABC中∠ABC等于九十&两位数^∘∠A等于三十二&两位数^点BC在⊙O上边ABAC分别交⊙O于DE两点点B是CD的中点则∠ABE等于_______";
//        String s= "如图在Rt?ABC中∠ABC等于九十&两位数^∠A等于三十二&两位数^点BC在⊙O上边ABAC分别交⊙O于DE两点点B是CD的中点则∠ABE等于_______";
//        String s= "如图在Rt\uD835\uDEE5ABC中∠ABC等于九十&两位数^∠A等于三十二&两位数^点BC在⊙O上边ABAC分别交⊙O于DE两点点B是CD的中点则∠ABE等于_______";
//        List<String> res = getBertFullTokenizer(s, true);
//        log.info(res.toString());
//        List<Long> l = getTokensIndex(res);
//        log.info(l.toString());
//        List<Integer> l = Arrays.asList(1, 2, 3, 4);
//        l = l.subList(0, 2);
//        System.out.println(l);
//        System.out.println(filePathURL);
//        File file = new File("./ttx");
//        System.out.println(file.getPath());
//        System.out.println(file.getAbsolutePath());
//        System.out.println(file.getCanonicalPath());
    }
}