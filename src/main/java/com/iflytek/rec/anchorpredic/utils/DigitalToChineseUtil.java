package com.iflytek.rec.anchorpredic.utils;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/9/22 15:59
 */
public class DigitalToChineseUtil {

    private DigitalToChineseUtil() {
    }

    /**
     * 中文数字
     */
    private static final String[] CN_NUM = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

    /**
     * 中文数字单位
     */
    private static final String[] CN_UNIT = {"", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千", "万", "十万", "百万", "千万", "万万", "兆", "十", "百", "千", "万", "十万", "百万", "千万", "万万", "十亿", "百亿", "千亿", "万亿", "亿亿"};
    private static final String[] CN_LEN = {"&一", "&两", "&三", "&四", "&五"};
    private static final String[] CN_LEN_PRI = {"&一", "&二", "&三", "&四", "&五", "&六", "&七", "&八", "&九", "&十", "&十一", "&十二"};

    private static String WEISHU = "位数";
    private static String XIAOSHU = "&小数";

    /**
     * 特殊字符：负
     */
    private static final String CN_NEGATIVE = "负";

    /**
     * 特殊字符：点
     */
    private static final String CN_POINT = "点";

    /**
     * int 转 中文数字
     * 支持到int最大值
     *
     * @param intNum 要转换的整型数
     * @return 中文数字
     */
    public static String int2chineseNum(Long intNum) {
        StringBuilder sb = new StringBuilder();
        boolean isNegative = false;
        if (intNum < 0) {
            isNegative = true;
            intNum *= -1;
        }
        int count = 0;
        while (intNum > 0) {
            int cnNum = (int) (intNum % 10);
            sb.insert(0, CN_NUM[cnNum] + CN_UNIT[count]);
            intNum = intNum / 10;
            count++;
        }
        if (intNum == 0 && sb.length() == 0) {
            sb.insert(0, CN_NUM[0]);
            return sb.toString();
        }
        if (isNegative) {
            sb.insert(0, CN_NEGATIVE);
        }
        return sb.toString().replaceAll("零[千百十]", "零").replaceAll("零+万", "万")
                .replaceAll("零+亿", "亿").replaceAll("亿万", "亿零")
                .replaceAll("零+", "零").replaceAll("零$", "");
    }

    /**
     * bigDecimal 转 中文数字
     * 整数部分只支持到int的最大值
     *
     * @param bigDecimalNum 要转换的BigDecimal数
     * @return 中文数字
     */
    public static String bigDecimal2chineseNum(BigDecimal bigDecimalNum) {
        if (bigDecimalNum == null) {
            return CN_NUM[0];
        }
        StringBuilder sb = new StringBuilder();

        //将小数点后面的零给去除
//        String numStr = bigDecimalNum.abs().stripTrailingZeros().toPlainString();
        String numStr = bigDecimalNum.abs().toPlainString();
        String[] split = numStr.split("\\.");

//        boolean isBigNum = false;
        if (numStr.length() > 12) {
            return numStr;
//            BigInteger big = new BigInteger(split[0].trim(), 10);
//            BigInteger bigSmall = big.min(BigInteger.valueOf(2147483647));
//            isBigNum = bigSmall.equals(BigInteger.valueOf(2147483647));
        }
//        if (isBigNum) {
//            char[] chars = split[0].toCharArray();
//            for (char aChar : chars) {
//                int index = Integer.parseInt(String.valueOf(aChar));
//                sb.append(CN_NUM[index]);
//            }
//            return sb.toString() + CN_LEN[Math.min(4, chars.length - 1)] + WEISHU;
//        }
        String integerStr = int2chineseNum(Long.parseLong(split[0]));
//        String integerStr = int2chineseNum(new BigInteger(split[0].trim(), 10));
        if (StringUtils.equals(integerStr, CN_NUM[0])) {
            sb.append(integerStr);
        } else {
            sb.append(integerStr).append(CN_LEN[Math.min(4, split[0].length() - 1)]).append(WEISHU);
        }
        //如果传入的数有小数，则进行切割，将整数与小数部分分离
        if (split.length == 2) {
            //有小数部分
            sb.append(CN_POINT);
            String decimalStr = split[1];
            char[] chars = decimalStr.toCharArray();
            for (char aChar : chars) {
                int index = Integer.parseInt(String.valueOf(aChar));
                sb.append(CN_NUM[index]);
            }
        }
        //判断传入数字为正数还是负数
        int signum = bigDecimalNum.signum();
        if (signum == -1) {
            sb.insert(0, CN_NEGATIVE);
        }
        return sb.toString();
    }

    /**
     * bigDecimal 转 中文数字
     * 整数部分只支持到int的最大值
     *
     * @param bigDecimalNum 要转换的BigDecimal数
     * @return 中文数字
     */
    public static String bigDecimal2chineseNumNorm(BigDecimal bigDecimalNum) {
        if (bigDecimalNum == null) {
            return CN_NUM[0];
        }
        StringBuilder sb = new StringBuilder();

        //将小数点后面的零给去除
//        String numStr = bigDecimalNum.abs().stripTrailingZeros().toPlainString();
        String numStr = bigDecimalNum.abs().toPlainString();
        String[] split = numStr.split("\\.");

//        boolean isBigNum = false;
        if (numStr.length() > 12) {
            return numStr;
//            BigInteger big = new BigInteger(split[0].trim(), 10);
//            BigInteger bigSmall = big.min(BigInteger.valueOf(2147483647));
//            isBigNum = bigSmall.equals(BigInteger.valueOf(2147483647));
        }
//        if (isBigNum) {
//            char[] chars = split[0].toCharArray();
//            for (char aChar : chars) {
//                int index = Integer.parseInt(String.valueOf(aChar));
//                sb.append(CN_NUM[index]);
//            }
//            return sb.toString() + CN_LEN[Math.min(4, chars.length - 1)] + WEISHU;
//        }
        String integerStr = int2chineseNum(Long.parseLong(split[0]));
//        String integerStr = int2chineseNum(new BigInteger(split[0].trim(), 10));
        if (StringUtils.equals(integerStr, CN_NUM[0])) {
            sb.append(integerStr);
        } else {
            sb.append(integerStr);
        }
        //如果传入的数有小数，则进行切割，将整数与小数部分分离
        if (split.length == 2) {
            //有小数部分
            sb.append(CN_POINT);
            String decimalStr = split[1];
            char[] chars = decimalStr.toCharArray();
            for (char aChar : chars) {
                int index = Integer.parseInt(String.valueOf(aChar));
                sb.append(CN_NUM[index]);
            }
        }
        //判断传入数字为正数还是负数
        int signum = bigDecimalNum.signum();
        if (signum == -1) {
            sb.insert(0, CN_NEGATIVE);
        }
        return sb.toString();
    }

    public static String bigDecimal2chineseNumPrimary(BigDecimal bigDecimalNum) {
        if (bigDecimalNum == null) {
            return CN_NUM[0];
        }
        StringBuilder sb = new StringBuilder();

        //将小数点后面的零给去除
//        String numStr = bigDecimalNum.abs().stripTrailingZeros().toPlainString();
        String numStr = bigDecimalNum.abs().toPlainString();
        String[] split = numStr.split("\\.");

        //todo 4
        if (numStr.length() > 12 && split.length == 1) {
            return numStr;
        }
        String integerStr = int2chineseNum(Long.parseLong(split[0]));
//        String integerStr = int2chineseNum(new BigInteger(split[0].trim(), 10));
        if (StringUtils.equals(integerStr, CN_NUM[0])) {
            sb.append(integerStr);
        } else {
            if (split.length == 2) {
                sb.append(integerStr);
            } else {
                sb.append(integerStr).append(CN_LEN_PRI[Math.min(11, split[0].length() - 1)]).append(WEISHU);
            }
        }
        //如果传入的数有小数，则进行切割，将整数与小数部分分离
        if (split.length == 2) {
            //有小数部分
            sb.append(CN_POINT);
            String decimalStr = split[1];
            char[] chars = decimalStr.toCharArray();
            for (char aChar : chars) {
                int index = Integer.parseInt(String.valueOf(aChar));
                sb.append(CN_NUM[index]);
            }
            sb.append(XIAOSHU);
        }
        //判断传入数字为正数还是负数
        int signum = bigDecimalNum.signum();
        if (signum == -1) {
            sb.insert(0, CN_NEGATIVE);
        }
        return sb.toString();
    }

    public static String bigDecimal2chineseNumPhysics(BigDecimal bigDecimalNum) {
        if (bigDecimalNum == null) {
            return CN_NUM[0];
        }
        StringBuilder sb = new StringBuilder();

        //将小数点后面的零给去除
        String numStr = bigDecimalNum.abs().toPlainString();
        String[] split = numStr.split("\\.");

        if (numStr.length() > 12) {
            return numStr;
        }
        String integerStr = int2chineseNum(Long.parseLong(split[0]));
        sb.append(integerStr);
        //如果传入的数有小数，则进行切割，将整数与小数部分分离
        if (split.length == 2) {
            //有小数部分
            sb.append(CN_POINT);
            String decimalStr = split[1];
            char[] chars = decimalStr.toCharArray();
            for (char aChar : chars) {
                int index = Integer.parseInt(String.valueOf(aChar));
                sb.append(CN_NUM[index]);
            }
        }
        //判断传入数字为正数还是负数
        int signum = bigDecimalNum.signum();
        if (signum == -1) {
            sb.insert(0, CN_NEGATIVE);
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        System.out.println(123466 / 10);
        System.out.println(bigDecimal2chineseNum(BigDecimal.valueOf(123456789123L)));
//        String s = "123";
////        String s = "21474836469";
//        System.out.println(bigDecimal2chineseNum(BigDecimal.valueOf(Long.parseLong(s))));
////        String s =  "999";
//        if (s.length() > 9) {
//            BigInteger big = new BigInteger(s.trim(), 10);
//            BigInteger bigTage = big.min(BigInteger.valueOf(2147483647));
//            System.out.println(big);
//            System.out.println(bigTage.equals(big));
//            System.out.println(bigTage.equals(BigInteger.valueOf(2147483647)));
//        }
//
////        System.out.println(Double.parseDouble(s));
////        int i = -12345;
//        int i = 21474;
        double d = 1.260;
        int w = 1;
        System.out.println(bigDecimal2chineseNumPrimary(new BigDecimal("2000000200002000000000000000000200000000000000000200000000000000000000020000000000000000000500000000000000002000000000000001000000000000000020000000000000000001000000000020000000200000000000000000002000000000000000000000000")));
//        System.out.println(bigDecimal2chineseNum(BigDecimal.valueOf(i)));
        System.out.println(bigDecimal2chineseNum(BigDecimal.valueOf(d)));
        System.out.println(bigDecimal2chineseNum(BigDecimal.valueOf(w)));
    }
}