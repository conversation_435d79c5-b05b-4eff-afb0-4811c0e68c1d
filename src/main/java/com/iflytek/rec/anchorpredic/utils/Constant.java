package com.iflytek.rec.anchorpredic.utils;

import java.util.Arrays;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/12/4 16:28
 */
public class Constant {
    public static final String MATH_SUBJECT_CODE = "02";
    public static final String PRIMAY_PHASE_CODE = "03";
    public static final String JUNIOR_PHASE_CODE = "04";
    //小学数学
    public static final String PRIMARY_PATTERN = "02_03";
    //初中数学
    public static final String JUNIOR_PATTERN = "02_04";
    //初中物理
    public static final String JUNIOR_PHYSICS_PATTERN = "05_04";
    //初中化学
    public static final String JUNIOR_CHEMISTRY_PATTERN = "06_04";
    public static final List<String> EXD_FOUR_SUB = Arrays.asList("06_04","06_05","05_05");
    //高中数学
    public static final String HIGH_MATH_PATTERN = "02_05";
    //高中物理
    public static final String HIGH_PHYSICS_PATTERN = "05_05";
    //高中化学
    public static final String HIGH_CHEMISTRY_PATTERN = "06_05";
    public static final String JUNIOR_BIOLOGY_PATTERN = "13_04";
    public static final String HIGH_BIOLOGY_PATTERN = "13_05";
    public static final String PRIMARY_SCIENCE_PATTERN = "19_03";
    public static final String JUNIOR_SCIENCE_PATTERN = "19_04";
    public static final String TOPIC_OCR_TYPE = "OCRTOPIC";
    public static final String[] GRADE = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "不存在"};

    //年级不存在
    public static final int NOEXIST = 10;


    public static final int POINT_MAGICNUM = 5;
    public static final int ONE = 1;
    public static final String anchorMagic = "1234567890987654321";

    public static final int INFER_LENGTH_SENIOR = 120;
    public static final int INFER_LENGTH_120 = 120;
    public static final int INFER_LENGTH_100 = 100;
    public static final int INFER_LENGTH_200 = 200;
    public static final int INFER_LENGTH_98 = 98;
    public static final int INFER_LENGTH_PRIMARY = 200;
    public static final int MAXNUM = 999999;
}
