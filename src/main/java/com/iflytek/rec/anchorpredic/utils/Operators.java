package com.iflytek.rec.anchorpredic.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2025/7/14
 */
@Slf4j
public class Operators {
    public static <K, V extends Comparable<? super V>> Map<K, V> sortByValue(Map<K, V> map) {
        List<Map.Entry<K, V>> list = new ArrayList<>(map.entrySet());

        // 使用自定义比较器按值排序
        list.sort(Map.Entry.<K, V>comparingByValue().reversed());

        // 使用LinkedHashMap保持插入顺序
        Map<K, V> result = new LinkedHashMap<>();
        for (Map.Entry<K, V> entry : list) {
            result.put(entry.getKey(), entry.getValue());
        }

        return result;
    }
}
