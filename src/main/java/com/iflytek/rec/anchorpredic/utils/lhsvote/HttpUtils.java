package com.iflytek.rec.anchorpredic.utils.lhsvote;

import cn.hutool.http.HttpRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class HttpUtils {
    /**
     * 日志
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpUtils.class);


    public static String post(String url, String bodyStr, Map<String, List<String>> headers, int timeout) {
        return HttpRequest.post(url)
                .header(headers)
                .body(bodyStr)
                .timeout(timeout)
                .execute().body();
    }

    public static String post(String url, String bodyStr, Map<String, List<String>> headers) {
        return post(url, bodyStr, headers, -1);
    }
}
