package com.iflytek.rec.anchorpredic.utils.lhsvote;

import com.alibaba.fastjson2.JSONArray;
import com.google.common.io.Resources;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.LhsAnchorInfo;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.NewAnchorInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

public class FileReaderUtilLhs {
    private static final Logger log = LoggerFactory.getLogger(FileReaderUtilLhs.class);


    /**
     * newanchorID与章节映射关系
     */
    private static final Map<String, String> nanchorIdToChapter = new HashMap<>();

    /**
     * newanchorID与难度映射关系
     */
    private static final Map<String, Integer> nanchorIdToDifficult = new HashMap<>();

    /**
     * 加载配置
     */
    private static final Properties PROPERTIES = new Properties();

    /**
     * oldanchorID与 newanchorID Map映射关系
     */
    private static final Map<String, List<NewAnchorInfo>> oanchorIdToNanchorId = new HashMap<>();
    private static final FileReaderUtilLhs instance = FileReaderUtilLhs.InstanceHolder.instance;

    FileReaderUtilLhs() {
    }

    static {
        try {
            for (String s : FileReader("lhsvote/oral_point_info.json")) {
                List<LhsAnchorInfo> anchorInfoList = JSONArray.parseArray(s, LhsAnchorInfo.class);
                int i = 0;
                Map<String, List<LhsAnchorInfo>> cacheChapterDiff = new HashMap<>();
                for (LhsAnchorInfo anchorInfo : anchorInfoList) {
                    i++;
                    cacheChapterDiff.computeIfAbsent(anchorInfo.getNewid(), k -> new ArrayList<>());
                    cacheChapterDiff.get(anchorInfo.getNewid()).add(anchorInfo);
                }
                int ij =0;
                for (Map.Entry<String, List<LhsAnchorInfo>> entry: cacheChapterDiff.entrySet()){
                    //这里优先选择chapter字符串小的作为锚点章节code和diff
                    LhsAnchorInfo  lhsAnchorInfo =  entry.getValue().stream().sorted(Comparator.comparing(LhsAnchorInfo::getChapter_code)).findAny().orElse(new LhsAnchorInfo());
                    List<LhsAnchorInfo> test = entry.getValue().stream().filter(item-> !StringUtils.equals(lhsAnchorInfo.getPress_code(), item.getPress_code())).collect(Collectors.toList());

                    if(!test.isEmpty()){
                        ij++;
                        System.out.println("--------"+ij+"---------");

                        System.out.println(entry.getValue().toString());
                    }
                    nanchorIdToChapter.put(entry.getKey(),lhsAnchorInfo.getChapter_code() );
                    nanchorIdToDifficult.put(entry.getKey(), Integer.valueOf(lhsAnchorInfo.getDifficult()));
                }
                log.info("lhs-oral_point_info.json size ,{} ,anchorIds cache success! Size:{}", i, nanchorIdToChapter.size());
                break;
            }
            for (String s : FileReader("lhsvote/point_info.json")) {
                List<LhsAnchorInfo> anchorInfoList = JSONArray.parseArray(s, LhsAnchorInfo.class);
                int i = 0;
                Map<String, List<LhsAnchorInfo>> cacheChapterDiff = new HashMap<>();
                for (LhsAnchorInfo anchorInfo : anchorInfoList) {
                    i++;
                    cacheChapterDiff.computeIfAbsent(anchorInfo.getNewid(), k -> new ArrayList<>());
                    cacheChapterDiff.get(anchorInfo.getNewid()).add(anchorInfo);
                    oanchorIdToNanchorId.computeIfAbsent(anchorInfo.getId(), k -> new ArrayList<>());
                    NewAnchorInfo newAnchorInfo = new NewAnchorInfo();
                    newAnchorInfo.setAnchorId(anchorInfo.getNewid());
                    List<String> bookinfo = Arrays.asList(anchorInfo.getChapter_code().split("_"));
                    newAnchorInfo.setBookCode(bookinfo.get(1));
                    newAnchorInfo.setBookVersionCode(anchorInfo.getPress_code());
                    newAnchorInfo.setChapterCode(anchorInfo.getChapter_code());
                    newAnchorInfo.setChapterCodeLength(anchorInfo.getChapter_code().length());
                    oanchorIdToNanchorId.get(anchorInfo.getId()).add(newAnchorInfo);
                }
                for (Map.Entry<String, List<LhsAnchorInfo>> entry: cacheChapterDiff.entrySet()){
                    //这里优先选择chapter字符串小的作为锚点章节code和diff todo 这里要按照版本过滤
                    LhsAnchorInfo  lhsAnchorInfo =  entry.getValue().stream().sorted(Comparator.comparing(LhsAnchorInfo::getChapter_code)).findAny().orElse(new LhsAnchorInfo());
                    nanchorIdToChapter.put(entry.getKey(),lhsAnchorInfo.getChapter_code() );
//                    List<LhsAnchorInfo> test = entry.getValue().stream().filter(item-> !StringUtils.equals(lhsAnchorInfo.getPress_code(), item.getPress_code())).collect(Collectors.toList());
//                    if(!test.isEmpty()){
//                        System.out.println("-----------------");
//                        System.out.println(entry.getValue().toString());
//                    }
                    nanchorIdToDifficult.put(entry.getKey(), Integer.valueOf(lhsAnchorInfo.getDifficult()));
                }
                log.info("lhs-point_info.json size ,{} ,anchorIds cache success! Size:{}", i, nanchorIdToChapter.size());
                break;
            }
        } catch (Exception e) {
            log.error("lhs-point_info.json cache failed!, {}", e.toString());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }


    public static FileReaderUtilLhs of() {
        return instance;
    }

    static class InstanceHolder {
        static FileReaderUtilLhs instance = new FileReaderUtilLhs();
    }

    public Map<String, String> getNanchorIdToChapter() {
        return nanchorIdToChapter;
    }

    public Map<String, Integer> getNanchorIdToDifficult() {
        return nanchorIdToDifficult;
    }

    public Map<String, List<NewAnchorInfo>> getOanchorIdToNanchorId() {
        return oanchorIdToNanchorId;
    }

    public Properties getProperties(){
        return PROPERTIES;
    }


    public static List<String> FileReader(String fileName) throws Exception {
        URL url = Resources.getResource(fileName).toURI().toURL();
        List<String> list = Resources.readLines(url, StandardCharsets.UTF_8);
        return list;
    }

    public static void DataApiConfigReader(String absDaApiPath) throws IOException {
        InputStream in = new BufferedInputStream(Files.newInputStream(Paths.get(absDaApiPath)));
        PROPERTIES.load(in);
    }

    public static void main(String[] args) throws IOException {
        FileReaderUtilLhs lhs = new FileReaderUtilLhs();
        DataApiConfigReader("C:\\Users\\<USER>\\Desktop\\anchorpredict\\aianchorpredict\\src\\main\\resources\\application.properties");
        System.out.println("002-001".compareTo("002-001") );
    }
}