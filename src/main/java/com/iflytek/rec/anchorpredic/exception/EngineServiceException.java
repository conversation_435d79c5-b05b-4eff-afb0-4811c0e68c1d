package com.iflytek.rec.anchorpredic.exception;

public class EngineServiceException extends Exception{
    /**
     * 序列化号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 异常编码
     */
    private int errorCode;


    public EngineServiceException() {
        super();
    }

    public EngineServiceException(int errorCode, String arg0) {
        super(arg0);
        this.errorCode = errorCode;
    }

    public EngineServiceException(String arg0) {
        super(arg0);
    }

    public EngineServiceException(String arg0, Throwable arg1) {
        super(arg0, arg1);
    }

    public EngineServiceException(int errorCode, String arg0, Throwable arg1) {
        super(arg0, arg1);
        this.errorCode = errorCode;
    }

    public EngineServiceException(Throwable arg0) {
        super(arg0);
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }
}
