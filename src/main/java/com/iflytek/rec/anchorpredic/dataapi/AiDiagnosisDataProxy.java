package com.iflytek.rec.anchorpredic.dataapi;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.DataApiDiagnosisParam;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.TopicAnchorInfo;
import com.iflytek.rec.anchorpredic.utils.lhsvote.FileReaderUtilLhs;
import com.iflytek.rec.anchorpredic.utils.lhsvote.HttpUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class AiDiagnosisDataProxy {
    /**
     * 日志
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(AiDiagnosisDataProxy.class);

    /**
     * URL
     */
    private static final String URL = "adaptive.recommend.dataapi.aidiagnosis.url";

    /**
     * APPKEY
     */
    private static final String APPKEY = "adaptive.recommend.dataapi.aidiagnosis.appKey";

    /**
     * SIGN
     */
    private static final String SIGN = "adaptive.recommend.dataapi.aidiagnosis.sign";

    /**
     * ROUTE
     */
    private static final String ROUTE = "adaptive.recommend.dataapi.aidiagnosis.route";

    /**
     * VERSION
     */
    private static final String VERSION = "adaptive.recommend.dataapi.aidiagnosis.version";

    /**
     * TIMEOUT
     */
    private static final String TIMEOUT = "adaptive.recommend.dataapi.aidiagnosis.timeout";

    /**
     * CONTENT_TYPE_LIST
     */
    private static final List<String> CONTENT_TYPE_LIST = Arrays.asList("application/json");

    /**
     * FAMILY
     */
    private static final String FAMILY = "u";

    /**
     * ANCHOR_ID
     */
    private static final String ANCHOR_ID = "anchor_id";

    /**
     * CFDS_LEVEL
     */
    private static final String CFDS_LEVEL = "cfds_level";

    /**
     * SOURCE
     */
    private static final String SOURCE = "source";

    /**
     * QUALIFIERS
     */
    private static final List<String> QUALIFIERS = Arrays.asList(ANCHOR_ID, CFDS_LEVEL, SOURCE);

    /**
     * TABLE_NAME
     */
    private static final String TABLE_NAME = "dw_xxj_ai_check_anchor_predict";

    /**
     * FEATURE_VERSION
     */
    private static final Integer FEATURE_VERSION = 1;

    /**
     * SUCCESS_CODE
     */
    private static final String SUCCESS_CODE = "200";

    /**
     * 通过topicIdList和versionCode调用dataAPI获取AI诊断数据
     *
     * @param topicIdList
     * @param versionCode
     * @return
     */
    public static String getAiDiagnosisDataByDataApi(List<String> topicIdList, String versionCode) {
        // 进行试题id去重
        List<String> topicIds = new ArrayList<>(new HashSet<>(topicIdList));
        // 构造调用dataAPI的入参
        List<String> rowKeys = new ArrayList<>();
        for (String topicId : topicIds) {
            rowKeys.add(topicId + "#" + versionCode);
        }
        return getAiDiagnosisDataByDataApi(rowKeys);
    }

    /**
     * 通过rowKeyss调用dataAPI获取AI诊断数据
     *
     * @param rowKeys
     * @return
     */
    public static String getAiDiagnosisDataByDataApi(List<String> rowKeys) {
        String result = null;
        try {
            long startTime = System.currentTimeMillis();
            String url = FileReaderUtilLhs.of().getProperties().getProperty(URL);
            int timeout = Integer.parseInt(FileReaderUtilLhs.of().getProperties().getProperty(TIMEOUT));
            Map<String, List<String>> headers = buildHeadersBeforeGetAiDiagnosisDataByDataApi();
            String bodyMapStr = buildBodyStrBeforeGetAiDiagnosisDataByDataApi(rowKeys);
            LOGGER.info("invoke getAiDiagnosisDataByDataApi url is {}, headers is {}, bodyMap is {},cost time:{}ms ", url, JSON.toJSONString(headers), bodyMapStr, System.currentTimeMillis() - startTime);
            result = HttpUtils.post(url, bodyMapStr, headers, timeout);
        } catch (Exception e) {
            LOGGER.error("post getAiDiagnosisDataByDataApi failed!", e);
        }
        LOGGER.info("invoke getAiDiagnosisDataByDataApi result is {}", result);
        return result;
    }

    public static void processTopicAnchorMapByResStr(String resStr, Map<String, TopicAnchorInfo> topicAnchorMap) {
        if (StringUtils.isNotEmpty(resStr)) {
            try {
                JSONObject resJson = JSONObject.parseObject(resStr);
                String code = resJson.getString("code");
                if (SUCCESS_CODE.equals(code)) {
                    LOGGER.info("invoke getAiDiagnosisDataByDataApi success!");
                    JSONObject data = resJson.getJSONObject("studydata");
                    Set<String> keySet = data.keySet();
                    for (String key : keySet) {
                        String[] strArr = key.split("#");
                        // 过滤掉rowkey不符合要求的情况
                        if (2 != strArr.length) {
                            continue;
                        }
                        String topicId = strArr[0];
                        String versionCode = strArr[1];
                        JSONObject jsonObject = data.getJSONObject(key);
                        String anchorPointId = jsonObject.getString(ANCHOR_ID);
                        Double cfdsLevel = jsonObject.getDouble(CFDS_LEVEL);
                        String source = jsonObject.getString(SOURCE);
                        TopicAnchorInfo topicAnchorInfo = new TopicAnchorInfo();
                        topicAnchorInfo.setAnchorPointId(anchorPointId);
                        topicAnchorInfo.setBookVersionCode(versionCode);
                        topicAnchorInfo.setCfdsLevel(cfdsLevel);
                        topicAnchorInfo.setTopicId(topicId);
                        topicAnchorInfo.setSource(source);
                        topicAnchorMap.put(topicId, topicAnchorInfo);
                    }
                } else {
                    LOGGER.info("invoke getAiDiagnosisDataByDataApi failed!");
                }
            } catch (Exception e) {
                LOGGER.error("process resStr error!", e);
            }
        }
    }

    /**
     * 调用dataAPI之前构造headers
     *
     * @return
     */
    public static Map<String, List<String>> buildHeadersBeforeGetAiDiagnosisDataByDataApi() {
        Map<String, List<String>> headers = new HashMap<>();
        headers.put("app-key", Arrays.asList(FileReaderUtilLhs.of().getProperties().getProperty(APPKEY)));
        headers.put("sign", Arrays.asList(FileReaderUtilLhs.of().getProperties().getProperty(SIGN)));
        headers.put("x-gw-route", Arrays.asList(FileReaderUtilLhs.of().getProperties().getProperty(ROUTE)));
        headers.put("version", Arrays.asList(FileReaderUtilLhs.of().getProperties().getProperty(VERSION)));
        headers.put("Content-Type", CONTENT_TYPE_LIST);
        return headers;
    }


    /**
     * 调用dataAPI之前构造bodyStr
     *
     * @param rowKeys
     * @return
     */
    public static String buildBodyStrBeforeGetAiDiagnosisDataByDataApi(List<String> rowKeys) {
        Map<String, Object> bodyMap = new HashMap<>();
        DataApiDiagnosisParam dataApiDiagnosisParam = new DataApiDiagnosisParam();
        dataApiDiagnosisParam.setFamily(FAMILY);
        dataApiDiagnosisParam.setRowKeys(rowKeys);
        dataApiDiagnosisParam.setQualifiers(QUALIFIERS);
        dataApiDiagnosisParam.setTableName(TABLE_NAME);
        dataApiDiagnosisParam.setFeatureVersion(FEATURE_VERSION);
        bodyMap.put("params", dataApiDiagnosisParam);
        bodyMap.put("meta", new Object());
        String bodyMapStr = JSON.toJSONString(bodyMap);
        return bodyMapStr;
    }
}
