package com.iflytek.rec.anchorpredic.model;

import lombok.Data;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/12/2 14:14
 */
public enum ModelScopeEnum {
    //小学数学
    PRIMARYSCHOOLMATH("02_03"),
    //初中数学
    JUNIORSCHOOLMATH("02_04"),
    //初中物理
    JUNIORSCHOOLPHYSICS("05_04"),
    //高中物理
    HIGHSCHOOLPHYSICS("05_05"),
    //高中数学
    HIGHSCHOOLMATH("02_05"),
    //高中化学
    HIGHSCHOOLCHEMISTRY("06_05"),
    //初中化学
    JUNIORSCHOOLCHEMISTRY("06_04"),
    //初中生物
    JUNIORSCHOOLBIOLOGY("13_04"),
    //高中生物
    HIGHSCHOOLBIOLOGY("13_05"),
    //小学科学
    PRIMARYSCHOOLSCIENCE("19_03"),
    //初中科学
    JUNIORSCHOOLSCIENCE("19_04"),
    //其他模型
    OTHERS("others");
    private final String modelScope;

    ModelScopeEnum(String modelScope) {
        this.modelScope = modelScope;
    }

    public String getModelScope() {
        return modelScope;
    }
}
