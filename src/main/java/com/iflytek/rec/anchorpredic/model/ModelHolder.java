package com.iflytek.rec.anchorpredic.model;

import com.alibaba.fastjson2.JSON;
import com.iflytek.infer.IInferHelper;
import com.iflytek.infer.domain.Content;
import com.iflytek.infer.domain.DataFrame;
import com.iflytek.infer.interfaces.impl.InferHelperImpl;
import com.iflytek.infer.interfaces.params.InferParam;
import com.iflytek.infer.interfaces.params.InferResult;
import com.iflytek.rec.anchorpredic.pojo.AnchorPredictParams;
import com.iflytek.rec.anchorpredic.pojo.ModelParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/10/13 16:30
 */
public class ModelHolder {
    private static final Logger log = LoggerFactory.getLogger(ModelHolder.class);

    private static final ModelHolder instance = ModelHolder.InstanceHolder.instance;

    public static IInferHelper inferHelper;


    public void init(ModelParams modelParams) {
        String modelConfig = modelParams.getWorkPath() + "/native/cfg/" + modelParams.getModelConfigName();
        try {
            log.info("【锚点预测模型加载成功】模型配置文件路径:{}", modelConfig);
            inferHelper.init(modelConfig);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("【锚点预测模型加载错误】当前模型配置文件路径错误:{}", modelConfig);
        }
    }

    public static ModelHolder of(String workPath) {
        inferHelper = InferHelperImpl.instance(workPath);
        return instance;
    }

    static class InstanceHolder {
        static ModelHolder instance = new ModelHolder();
    }

    /**
     * 锚点预测模型
     */
    public static InferResult anchorPredicModel(InferParam inputs, AnchorPredictParams anchorPredictParams) {
//        String modelConfig = MODEL_CONFIG_FILE_PATH + anchorPredictParams.getModelConfigName();
        String modelConfig = "";
        log.info("【锚点预测模型加载】模型配置文件路径:{}", modelConfig);
        log.info("【锚点预测模型加载】模型key值:{}", "");
        IInferHelper inferHelper = InferHelperImpl.instance("");
        inferHelper.init(modelConfig);
        InferResult inferResult = new InferResult();
        try {
            inferResult = inferHelper.predict("", inputs);
        } catch (Exception e) {
            e.printStackTrace();
        }
//        inferHelper.fini();
        return inferResult;
    }

    public static InferResult anchorPredicModelTest(InferParam inputs, AnchorPredictParams anchorPredictParams) {
//        String modelConfig = MODEL_CONFIG_FILE_PATH + anchorPredictParams.getModelConfigName();
        String modelConfig = "";
        log.info("【锚点预测模型加载】模型入参:{}", JSON.toJSONString(inputs));
        log.info("【锚点预测模型加载】模型配置文件路径:{}", modelConfig);
        log.info("【锚点预测模型加载】模型key值:{}", "");
        InferResult inferResult = new InferResult();
        try {
            List<Content> inferContent = new ArrayList<>();
            inferResult.setInferContent(inferContent);
            Content content = new Content();
            inferContent.add(content);
            List<DataFrame> outputs = new ArrayList<>();
            content.setOutputs(outputs);
            DataFrame dataFrame = new DataFrame();
            List<Float> floatValue = Arrays.asList(0.9f, 0.8f, 0.7f, 0.6f, 0.5f, 0.4f, 0.3f, 0.2f, 0.1f);
            dataFrame.setFloatValue(floatValue);
            outputs.add(dataFrame);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return inferResult;
    }

}
