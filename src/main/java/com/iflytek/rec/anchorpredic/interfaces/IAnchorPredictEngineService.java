package com.iflytek.rec.anchorpredic.interfaces;

import com.iflytek.rec.anchorpredic.pojo.AnchorPredictParams;
import com.iflytek.rec.anchorpredic.pojo.AnchorPredictResponse;
import com.iflytek.rec.anchorpredic.pojo.AnchorPredictReturn;
import com.iflytek.rec.anchorpredic.pojo.ModelParams;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/10/13 14:36
 */
@Validated
public interface IAnchorPredictEngineService {

    /**
     * 模型初始化
     * 服务启动时初始化 加载模型
     * modelConfigName变更时初始化 加载模型
     * 服务侧控制是否初始化模型
     */
    void initModel(ModelParams modelParams);

    /**
     * 锚点预测引擎服务接口
     * 入参解析、模型预测、锚点过滤、锚点投票
     */
    AnchorPredictReturn anchorPredictEngineService(@Valid @NotNull AnchorPredictParams anchorPredictParams);
    /**
     * 锚点预测引擎服务接口
     * 入参解析、模型预测、锚点过滤、锚点投票
     */
    AnchorPredictResponse anchorPredictTopNEngine(@Valid @NotNull AnchorPredictParams anchorPredictParams);

    /**
     * 模型释放
     * 服务停机、服务重启 模型释放 否则将造成内存泄露
     * 服务侧控制是否释放模型
     */
    void closeModel();
}
