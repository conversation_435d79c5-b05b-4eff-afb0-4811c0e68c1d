package com.iflytek.rec.anchorpredic.interfaces.lhsvote;

import com.iflytek.rec.anchorpredic.pojo.lhsvote.PhotoTopicOutput;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.PointRecgRequest;

import java.util.List;

public interface AnchorPredictBusiness {

    /**
     * 通过查询离线锚点预测结果，并对查询结果进行投票
     *
     * @param request 入参请求
     * @return 预测结果
     */
    List<PhotoTopicOutput> offlinePredict(PointRecgRequest request);


}
