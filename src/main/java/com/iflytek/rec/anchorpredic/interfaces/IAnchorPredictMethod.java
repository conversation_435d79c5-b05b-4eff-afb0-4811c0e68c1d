package com.iflytek.rec.anchorpredic.interfaces;

import com.iflytek.infer.domain.Content;
import com.iflytek.rec.anchorpredic.pojo.AnchorPredictParams;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/9/26 15:09
 */
public interface IAnchorPredictMethod {
    /**
     * 入参解析模块-实验备用
     * 1、去除题号和得分  保留中间题干
     * 2、整数和小数转为汉字
     * 3、latex公式不解析转化
     */
    String dataAnalysisExceptLatex(String htmlString) throws IOException;

    /**
     * 入参解析模块-实验备用
     * 1、去除题号和得分  保留中间题干
     * 2、整数和小数转为汉字
     * 3、latex公式解析转化
     */
//    String dataAnalysisIncludeLatex(String htmlString) throws IOException;

    /**
     * 入参解析模块1
     * 1、去除题号和得分  保留中间题干
     * 2、整数和小数转为汉字
     * 3、latex公式解析转化使用map映射表
     */
    String dataAnalysisIncludeLatexUMap(String htmlString,String resourcePattern) throws IOException;

    /**
     * 入参解析模块2
     * ocr文本解析 提取题干
     * 整数和小数转为汉字
     * latex公式解析转化使用map映射表
     */
    String dataAnalysisOcr(String ocrString,String resourcePattern);

    /**
     * 锚点模型推理模块
     * 输入：模型输出的得分 + 题目列表
     * 输出：试题锚点概率map（锚点数量3000+）Map<试题id,Map<锚点id,锚点概率>>
     */
    Map<String, Map<String, Float>> anchorPredictModelOutAnalysis(List<Content> modelTopicsAnchorsScore, List<String> topics,String resourcePattern);
    /**
     * 锚点过滤模块
     * 入参1：业务支持的锚点列表 过滤用 anchorPredictParams.getForbiddenAnchors
     * 入参2：试题锚点概率map（锚点数量3000+）Map<试题id,Map<锚点id,锚点概率>>
     * 出参：试题锚点概率map（过滤后的锚点数量 仅剩1个锚点）Map<试题id,Map<锚点id,锚点概率>>
     */
    Map<String, Map<String, Float>> anchorPredictFilter(AnchorPredictParams anchorPredictParams, Map<String, Map<String, Float>> topicsAnchorsScoreMap,String resourcePattern);

    /**
     * 锚点投票模块
     * 入参1：试题锚点概率map（锚点数量3000+）Map<试题id,Map<锚点id,锚点概率>>
     * 入参2：试题锚点概率map（过滤后的锚点数量 仅剩1个锚点）Map<试题id,Map<锚点id,锚点概率>>
     * 出参：锚点预测结果 Map<试题id,锚点id>
     */
    Map<String, String> anchorPredictVote(AnchorPredictParams anchorPredictParams, Map<String, Map<String, Float>> topicsMap, Map<String, Map<String, Float>> topicsAnchorsScoreMapFilter,String resourcePattern);
    /**
     * 锚点投票模块
     * 输出topN锚点
     */
    Map<String, List<String> > anchorPredictVoteTopN(AnchorPredictParams anchorPredictParams, Map<String, Map<String, Float>> topicsMap, Map<String, Map<String, Float>> topicsAnchorsScoreMapFilter,String resourcePattern);

}