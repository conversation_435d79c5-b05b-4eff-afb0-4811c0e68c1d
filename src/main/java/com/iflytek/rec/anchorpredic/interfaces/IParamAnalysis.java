package com.iflytek.rec.anchorpredic.interfaces;

import java.io.IOException;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2023/1/3 15:01
 */
public interface IParamAnalysis {
    /**
     * 入参解析模块1
     * html文本解析 提取题干
     * 整数和小数转为汉字
     * latex公式解析转化使用map映射表
     */
    String paramAnalysisHtml(String htmlString, String resourcePattern) throws IOException;

    /**
     * 入参解析模块2
     * ocr文本解析 提取题干
     * 整数和小数转为汉字
     * latex公式解析转化使用map映射表
     */
    String paramAnalysisOcr(String ocrString, String resourcePattern);
}
