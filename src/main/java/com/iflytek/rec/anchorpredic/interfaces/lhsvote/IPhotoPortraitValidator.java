package com.iflytek.rec.anchorpredic.interfaces.lhsvote;

import com.iflytek.rec.anchorpredic.exception.EngineServiceException;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.*;

public interface IPhotoPortraitValidator {

    /**
     * 拍搜画像-点识别接口参数校验
     * 参数校验见接口文档：http://in.zhixue.com/api/web/#/49?page_id=5262
     *
     * @param request 点识别接口请求入参
     * @throws EngineServiceException 参数校验异常
     */
    void pointRecgValidation(PointRecgRequest request) throws EngineServiceException;

}