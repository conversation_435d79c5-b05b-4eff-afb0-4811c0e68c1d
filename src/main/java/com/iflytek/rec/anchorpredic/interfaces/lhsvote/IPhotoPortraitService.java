package com.iflytek.rec.anchorpredic.interfaces.lhsvote;

import com.iflytek.rec.anchorpredic.exception.EngineServiceException;
import com.iflytek.rec.anchorpredic.pojo.ModelParams;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.PointRecgRequest;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.PointRecgResponse;

public interface IPhotoPortraitService {
    /**
     * dataApi接口初始化
     */
    void initModel(ModelParams modelParams);
    /**
     * 拍搜画像-点识别接口
     * 理化生迁移到锚点预测引擎
     *
     * @param request 点识别接口请求
     * @return 点识别结果
     */
    PointRecgResponse pointRecg(PointRecgRequest request) throws EngineServiceException;
}
