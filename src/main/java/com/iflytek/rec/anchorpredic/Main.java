package com.iflytek.rec.anchorpredic;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.iflytek.rec.anchorpredic.impl.lhsvote.PhotoPortraitServiceImpl;
import com.iflytek.rec.anchorpredic.interfaces.lhsvote.IPhotoPortraitService;
import com.iflytek.rec.anchorpredic.pojo.*;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.*;
import com.iflytek.rec.anchorpredic.datafactory.holder.FileReaderUtil;
import com.iflytek.rec.anchorpredic.impl.AnchorPredictEngineServiceImpl;
import com.iflytek.rec.anchorpredic.impl.LatexJsonAnalysImpl;
import com.iflytek.rec.anchorpredic.interfaces.IAnchorPredictEngineService;
import com.iflytek.rec.anchorpredic.interfaces.ILatexJsonAnalys;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.iflytek.rec.anchorpredic.datafactory.holder.FileReaderUtil.supportAnchors;
import static com.iflytek.rec.anchorpredic.utils.lhsvote.FileReaderUtilLhs.FileReader;


/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/10/13 11:19
 */
public class Main {
    private static final Logger log = LoggerFactory.getLogger(Main.class);

    public static List<AnchorPredictParams> initparms(String fileName) throws Exception {
        List<AnchorPredictParams> anchorPredictParams;
        if (StringUtils.equals(fileName, "sig")) {
            anchorPredictParams = FileReaderUtil.consistenceTestSigle();
        } else if (StringUtils.equals(fileName, "all")) {
            anchorPredictParams = FileReaderUtil.consistenceTestAll();
        } else {
            anchorPredictParams = FileReaderUtil.consistenceConsole(fileName);
        }

        for (AnchorPredictParams anchorPredictParams1 : anchorPredictParams) {
            UserInfo userInfo = anchorPredictParams1.getUserInfo();
            String pattern = userInfo.getSubjectCode() + "_" + userInfo.getPhaseCode();
            if (anchorPredictParams1.getSupportAnchors().isEmpty()) {
                supportAnchor(anchorPredictParams1, pattern);
            }
        }
        return anchorPredictParams;
    }

    public static List<AnchorPredictParams> initparmsTopN(String fileName) throws Exception {
        List<AnchorPredictParams> anchorPredictParams;
        if (StringUtils.equals(fileName, "sig")) {
            anchorPredictParams = FileReaderUtil.consistenceTestSigle();
        } else if (StringUtils.equals(fileName, "all")) {
            anchorPredictParams = FileReaderUtil.consistenceTestAll();
        } else {
            anchorPredictParams = FileReaderUtil.consistenceConsole(fileName);
        }

        return anchorPredictParams;
    }

    private static void supportAnchor(AnchorPredictParams anchorPredictParams, String patten) throws Exception {
        List<BookAnchorMap> anchorMaps = supportAnchors(patten);
        Map<String, List<String>> bookanchormap = new HashMap<>();
        for (BookAnchorMap bookAnchorMap : anchorMaps) {
            bookanchormap.put(bookAnchorMap.getBookV(), bookAnchorMap.getSupporanhcors());
        }
        anchorPredictParams.setSupportAnchors(bookanchormap.get(anchorPredictParams.getUserInfo().getBookCode()));
    }

    public static void ThreadTest(Integer num, String fileName, Boolean isStaTest) {
        //多线程测试
        for (int i = 0; i < num; i++) {
            MyThread t = new MyThread(fileName, isStaTest);
            // 启动线程
            t.start();
        }
    }

    @Test
    public void test01() throws Exception {
        initparms("all");
    }
    public static void main(String[] args) {

        IAnchorPredictEngineService anchorPredictEngineService = new AnchorPredictEngineServiceImpl();
        try {

            log.info("AI diag main start!");
            log.info("params:{}", (Object) args);
            log.info("Test test success! please given param1-function : infer");
            log.info("Test test success! please given param2-fileChoice : all || sig || XXX.json");
            log.info("Test test success! please given param3-ouput : default:ouput.json");
            log.info("Test test success! please given param4-threadNums : default:0");
            log.info("Test test success! please given stability-test (no/yes): default:no");
            log.info("Test test success! please given param6-workpath : default:/data/ygyang5/20250716_AiAnchorPredict_engine_optimize");


            List<String> argList = Arrays.asList(args);
            List<String> argListDefault = new ArrayList<>();
            argListDefault.add("infer");
            argListDefault.add("sig");
            argListDefault.add("ouput.json");
            argListDefault.add("0");
            argListDefault.add("no");
            argListDefault.add("/data/ygyang5/20250716_AiAnchorPredict_engine_optimize");
//            argListDefault.add("/data/xzmou/gc-anc_c/bin");

            int i = 0;
            for (String s : argList) {
                argListDefault.set(i, s);
                i++;
            }
            if (!argList.isEmpty() && StringUtils.equals(argListDefault.get(0), "infer")) {
//            if (true) {
                ModelParams modelParams = new ModelParams();
                modelParams.setWorkPath(argListDefault.get(5));
                //模型初始化
                anchorPredictEngineService.initModel(modelParams);
                boolean isStabilityTest = StringUtils.equals(argListDefault.get(4), "yes");

                //添加num个子线程并发
                int threadNums = Integer.parseInt(argListDefault.get(3));
                ThreadTest(threadNums, argListDefault.get(1), isStabilityTest);

                String fileName = argListDefault.get(2);
                File file = new File(fileName);
                if (!file.exists()) {
                    file.createNewFile();
                }
                FileWriter fileWriter = new FileWriter(file.getAbsoluteFile(), true);
                BufferedWriter bw = new BufferedWriter(fileWriter);
                List<AnchorPredictParams> anchorPredictParams = initparms(argList.get(1));
                List<Long> timeL = new ArrayList<>();
                //压力测试
//                while (true) {
//                    for (AnchorPredictParams anchorPredictParams1 : anchorPredictParams) {
//                        Instant times = Instant.now();
//                        AnchorPredictReturn anchorPredictReturn = anchorPredictEngineService.anchorPredictEngineService(anchorPredictParams1);
//                        Instant timee = Instant.now();
//                        log.info("分支线程-- 单次耗时(ms):{}", ChronoUnit.MILLIS.between(times, timee));
//                    }
//                }
                while (isStabilityTest) {
                    for (AnchorPredictParams anchorPredictParams1 : anchorPredictParams) {
                        //禁用学期信息
                        anchorPredictParams1.setFullPageSearchRes("");
//                        Map<String, Double> cdfs = new HashMap<>();
//                        for (String str : anchorPredictParams1.getHtmlStrings().keySet()) {
//                            cdfs.put(str, 1d);
//                        }
//                        anchorPredictParams1.setCfdsLevels(cdfs);
                        Instant times = Instant.now();

                        AnchorPredictReturn anchorPredictReturn = anchorPredictEngineService.anchorPredictEngineService(anchorPredictParams1);
                        Instant timee = Instant.now();
                        timeL.add(ChronoUnit.MILLIS.between(times, timee));
                    }
                    log.info("推荐{}次 每次耗时(ms):{}", timeL.size(), timeL);
                }
                for (AnchorPredictParams anchorPredictParams1 : anchorPredictParams) {
                    //禁用学期信息
                    anchorPredictParams1.setFullPageSearchRes("");
//                    Map<String, Double> cdfs = new HashMap<>();
//                    for (String str : anchorPredictParams1.getHtmlStrings().keySet()) {
//                        cdfs.put(str, 1d);
//                    }
//                    anchorPredictParams1.setCfdsLevels(cdfs);
                    log.info("入参：{}", anchorPredictParams1);
                    Instant times = Instant.now();
                    AnchorPredictReturn anchorPredictReturn = anchorPredictEngineService.anchorPredictEngineService(anchorPredictParams1);
                    bw.write(JSON.toJSONString(anchorPredictReturn) + "\n");
                    Instant timee = Instant.now();
                    timeL.add(ChronoUnit.MILLIS.between(times, timee));
                }
                bw.close();
                log.info("推荐{}次 每次耗时(ms):{}", timeL.size(), timeL);
                TimeUnit.SECONDS.sleep(3);
                anchorPredictEngineService.closeModel();

            }else if (!argList.isEmpty() && StringUtils.equals(argListDefault.get(0), "infertopn")) {
                ModelParams modelParams = new ModelParams();
                modelParams.setWorkPath(argListDefault.get(5));
                //模型初始化
                anchorPredictEngineService.initModel(modelParams);
                boolean isStabilityTest = StringUtils.equals(argListDefault.get(4), "yes");

                //添加num个子线程并发
                int threadNums = Integer.parseInt(argListDefault.get(3));
                ThreadTest(threadNums, argListDefault.get(1), isStabilityTest);

                String fileName = argListDefault.get(2);
                File file = new File(fileName);
                if (!file.exists()) {
                    file.createNewFile();
                }
                FileWriter fileWriter = new FileWriter(file.getAbsoluteFile(), true);
                BufferedWriter bw = new BufferedWriter(fileWriter);
                List<AnchorPredictParams> anchorPredictParams = initparmsTopN(argList.get(1));
                List<Long> timeL = new ArrayList<>();
                //压力测试
                for (AnchorPredictParams anchorPredictParams1 : anchorPredictParams) {
                    //禁用学期信息
                    anchorPredictParams1.setFullPageSearchRes("");
//                    Map<String, Double> cdfs = new HashMap<>();
//                    for (String str : anchorPredictParams1.getHtmlStrings().keySet()) {
//                        cdfs.put(str, 1d);
//                    }
//                    anchorPredictParams1.setCfdsLevels(cdfs);
//                    anchorPredictParams1.setTopN(3);
                    log.info("入参：{}", anchorPredictParams1);
                    Instant times = Instant.now();
                    AnchorPredictResponse anchorPredictReturn = anchorPredictEngineService.anchorPredictTopNEngine(anchorPredictParams1);
                    bw.write(JSON.toJSONString(anchorPredictReturn) + "\n");
                    Instant timee = Instant.now();
                    timeL.add(ChronoUnit.MILLIS.between(times, timee));
                }
                bw.close();
                log.info("推荐{}次 每次耗时(ms):{}", timeL.size(), timeL);
                TimeUnit.SECONDS.sleep(3);
                anchorPredictEngineService.closeModel();

            }
            else if (!argList.isEmpty() && StringUtils.equals(argList.get(0), "latex")) {
                ILatexJsonAnalys iLatexJsonAnalys = new LatexJsonAnalysImpl();
                String path = System.getProperty("user.dir") + "/" + argList.get(1);
                log.info("当前JSON文件路径:{}", path);
                List<String> res = FileReaderUtil.FileReaderAbsolutePath(path);
                File file = new File("latexUseMap.json");
//                File file1 = new File("latexUseOnlooker.json");
                if (!file.exists()) {
                    file.createNewFile();
                }
//                if (!file1.exists()) {
//                    file1.createNewFile();
//                }
                FileWriter fileWriter = new FileWriter(file.getAbsoluteFile(), true);
//                FileWriter fileWriter1 = new FileWriter(file1.getAbsoluteFile(), true);
                BufferedWriter bw = new BufferedWriter(fileWriter);
//                BufferedWriter bw1 = new BufferedWriter(fileWriter1);

                for (String s : res) {
                    List<JsonToLatex> jsonLatex = JSONArray.parseArray(s, JsonToLatex.class);
                    for (JsonToLatex jsonToLatex : jsonLatex) {
                        JsonToLatex j = new JsonToLatex();
                        JsonToLatex j1 = new JsonToLatex();
                        log.info("原始输入:{}", jsonToLatex.getInput());
                        log.info("latext使用map映射解析:{}", iLatexJsonAnalys.latexJsonUseMap(jsonToLatex.getInput()));
//                        log.info("latext使用第三方包解析:{}", iLatexJsonAnalys.latexJsonUseWeb(jsonToLatex.getInput()));
                        j.setInput(jsonToLatex.getInput());
                        j1.setInput(jsonToLatex.getInput());
//                        j.setOutput(iLatexJsonAnalys.latexJsonUseWeb(jsonToLatex.getInput()));
                        j1.setOutput(iLatexJsonAnalys.latexJsonUseMap(jsonToLatex.getInput()));
                        bw.write(JSON.toJSONString(j1).replace("[", "").replace("]", "") + "\n");
//                        bw1.write(JSON.toJSONString(j).replace("[", "").replace("]", "") + "\n");
                    }

                }
                bw.close();
//                bw1.close();
            } else if (!argList.isEmpty() && StringUtils.equals(argListDefault.get(0), "lhsvote")) {
//            else if (true) {
                IPhotoPortraitService photoPortraitService = new PhotoPortraitServiceImpl();
                ModelParams modelParams = new ModelParams();
//                FileReaderUtilLhs.DataApiConfigReader("D:\\JavaProjects\\aianchorpredict\\aianchorpredict\\src\\main\\resources\\application.properties");

                //模型初始化
                photoPortraitService.initModel(modelParams);
                List<PointRecgResponse> pointRecgResponses = new ArrayList<>();
                List<PointRecgRequest> pointRecgRequests = new ArrayList<>();
                for (String s : FileReader("utilsdata/testfile/lhstest.json")) {
                    List<PointRecgRequest> pointRecgRequestList = JSONArray.parseArray(s, PointRecgRequest.class);
                    int k = 0;
                    for (PointRecgRequest pointRecgRequest : pointRecgRequestList) {
                        k++;
                        log.info("-----------------------【理化生锚点预测开始线】-----入参：--【{}】-------------------------------", k);
                        PointRecgResponse response = photoPortraitService.pointRecg(pointRecgRequest);
                        if (!response.getPhotoTopicOutputList().isEmpty()) {
                            pointRecgResponses.add(response);
                            pointRecgRequests.add(pointRecgRequest);
                        }
                    }
                }
                log.info("拍搜点识别接口-测试数据有真实返回case入参：{}", pointRecgRequests);
                log.info("拍搜点识别接口-测试数据有真实返回case出参：{}", pointRecgResponses);
            } else {
                log.info("Test test success! please given param : latex ");
                log.info("And given json name : XXX");
                log.info("Or Test test success! please given param : infertest");
            }


        } catch (Exception e) {
            anchorPredictEngineService.closeModel();
            log.error("test fail!:{}", e.toString());
            e.printStackTrace();
        }
    }
}

class MyThread extends Thread {
    private static final Logger log = LoggerFactory.getLogger(MyThread.class);
    private static List<AnchorPredictParams> anchorPredictParamStatic = new ArrayList<>();
    private static Boolean StaTest;


    public static void initparms(String fileName, Boolean isStaTest) throws Exception {
        List<AnchorPredictParams> anchorPredictParams;
        if (StringUtils.equals(fileName, "sig")) {
            anchorPredictParams = FileReaderUtil.consistenceTestSigle();
        } else if (StringUtils.equals(fileName, "all")) {
            anchorPredictParams = FileReaderUtil.consistenceTestAll();
        } else {
            anchorPredictParams = FileReaderUtil.consistenceConsole(fileName);
        }

        for (AnchorPredictParams anchorPredictParams1 : anchorPredictParams) {
            UserInfo userInfo = anchorPredictParams1.getUserInfo();
            String pattern = userInfo.getSubjectCode() + "_" + userInfo.getPhaseCode();
            if (anchorPredictParams1.getSupportAnchors().isEmpty()) {
                supportAnchor(anchorPredictParams1, pattern);
            }
        }
        anchorPredictParamStatic = anchorPredictParams;
        StaTest = isStaTest;
    }

    private static void supportAnchor(AnchorPredictParams anchorPredictParams, String patten) throws Exception {
        List<BookAnchorMap> anchorMaps = supportAnchors(patten);
        Map<String, List<String>> bookanchormap = new HashMap<>();
        for (BookAnchorMap bookAnchorMap : anchorMaps) {
            bookanchormap.put(bookAnchorMap.getBookV(), bookAnchorMap.getSupporanhcors());
        }
        anchorPredictParams.setSupportAnchors(bookanchormap.get(anchorPredictParams.getUserInfo().getBookCode()));
    }

    public MyThread(String fileName, Boolean isStaTest) {
        try {
            initparms(fileName, isStaTest);
        } catch (Exception e) {
            log.error(String.valueOf(e));
            e.printStackTrace();
        }

    }

    @Override
    public void run() {
        // 编写程序，这段程序运行在分支线程中（分支栈）。
        IAnchorPredictEngineService anchorPredictEngineService = new AnchorPredictEngineServiceImpl();
        try {
            log.info("分支线程--AI diag main start!");
            log.info("分支线程--Test test success! please given param : infertest");
            List<Long> timeL = new ArrayList<>();
            //压力测试
//            while (true) {
//                for (AnchorPredictParams anchorPredictParams1 : anchorPredictParams) {
//                    Instant times = Instant.now();
//                    AnchorPredictReturn anchorPredictReturn = anchorPredictEngineService.anchorPredictEngineService(anchorPredictParams1);
//                    Instant timee = Instant.now();
//                    log.info("分支线程-- 单次耗时(ms):{}", ChronoUnit.MILLIS.between(times, timee));
//                }
//            }
            while (StaTest) {
                for (AnchorPredictParams anchorPredictParams1 : anchorPredictParamStatic) {
                    Map<String, Double> cdfs = new HashMap<>();
                    for (String str : anchorPredictParams1.getHtmlStrings().keySet()) {
                        cdfs.put(str, 1d);
                    }
                    anchorPredictParams1.setCfdsLevels(cdfs);
                    Instant times = Instant.now();
                    //禁用学期信息
                    anchorPredictParams1.setFullPageSearchRes("");
                    AnchorPredictReturn anchorPredictReturn = anchorPredictEngineService.anchorPredictEngineService(anchorPredictParams1);
                    log.info("分支线程--Infer Test success!");
                    Instant timee = Instant.now();
                    timeL.add(ChronoUnit.MILLIS.between(times, timee));
                }
                log.info("分支线程--推荐{}次 每次耗时(ms):{}", timeL.size(), timeL);
            }
            for (AnchorPredictParams anchorPredictParams1 : anchorPredictParamStatic) {
                Map<String, Double> cdfs = new HashMap<>();
                for (String str : anchorPredictParams1.getHtmlStrings().keySet()) {
                    cdfs.put(str, 1d);
                }
                anchorPredictParams1.setCfdsLevels(cdfs);
                Instant times = Instant.now();
                //禁用学期信息
                anchorPredictParams1.setFullPageSearchRes("");
                AnchorPredictReturn anchorPredictReturn = anchorPredictEngineService.anchorPredictEngineService(anchorPredictParams1);
                log.info("分支线程--Infer Test success!");
                log.info("分支线程--推荐结果:{}", JSON.toJSONString(anchorPredictReturn));
                Instant timee = Instant.now();
                timeL.add(ChronoUnit.MILLIS.between(times, timee));
            }
            log.info("分支线程--推荐{}次 每次耗时(ms):{}", timeL.size(), timeL);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(String.valueOf(e));
            throw new RuntimeException(e);
        }
    }
}

