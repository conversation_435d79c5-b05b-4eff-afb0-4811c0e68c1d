README for the XSLT MathML Library

XSLT MathML Library is a set of XSLT stylesheets to transform
MathML 2.0 to LaTeX.

For more information, see
http://www.raleigh.ru/MathML/mmltex/index.php?lang=en

Manifest
--------

README        this file
mmltex.xsl
tokens.xsl
glayout.xsl
scripts.xsl
tables.xsl
entities.xsl
cmarkup.xsl

Use
---

There are two ways of using the library:

    * Use a local copy of the library.

        1. Download the distribution (see below).

        2. Unpack the distribution, using unzip.

        3. In your stylesheet import or include either the main
           stylesheet, mmltex.xsl, or the stylesheet module you
           wish to use, such as tokens.xsl. This example assumes
           that the distribution has been extracted into the same
           directory as your own stylesheet:

           <xsl:import href="mmltex.xsl"/>

    * Import or include either the main stylesheet, or the
      stylesheet module you wish to use, directly from the library
      website; http://www.raleigh.ru/MathML/mmltex/. For example:

      <xsl:import href="http://www.raleigh.ru/MathML/mmltex/mmltex.xsl"/>

Obtaining The Library
---------------------

The XSLT MathML Library is available for download as:

    * Zip file: http://www.raleigh.ru/MathML/mmltex/mmltex.zip

Copyright
---------

Copyright (C) 2001-2003 Vasil Yaroshevich

Permission is hereby granted, free of charge, to any person
obtaining a copy of this software and associated documentation
files (the ``Software''), to deal in the Software without
restriction, including without limitation the rights to use,
copy, modify, merge, publish, distribute, sublicense, and/or
sell copies of the Software, and to permit persons to whom the
Software is furnished to do so, subject to the following
conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

Except as contained in this notice, the names of individuals
credited with contribution to this software shall not be used in
advertising or otherwise to promote the sale, use or other
dealings in this Software without prior written authorization
from the individuals in question.

Any stylesheet derived from this Software that is publically
distributed will be identified with a different name and the
version strings in any derived Software will be changed so that
no possibility of confusion between the derived package and this
Software will exist.

Warranty
--------

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT.  IN NO EVENT SHALL NORMAN WALSH OR ANY OTHER
CONTRIBUTOR BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
OTHER DEALINGS IN THE SOFTWARE.

Contacting the Author
---------------------

These stylesheets are maintained by Vasil Yaroshevich, <<EMAIL>>.
