{"fullPageSearchRes": "", "userInfo": {"phaseCode": "04", "semester": "TEST", "bookCode": "30", "subjectCode": "02"}, "supportAnchors": [], "htmlStrings": {"0": "<div><html>\n <head></head>\n <body>\n  <div class=\"question_3_6Ay\">\n    定义一种运算,如：x=(1,3,-2,0).y=(-2,-3,4,1),z=(2,-1,6,4),x+y=(-1,0,2,1)y+z=（-4,1,0,5）.\n   <br>\n   <br> 定义一种运算,如：\n   <br>\n   <br> x=(1,3,-2,0).y=(-2,-3,4,1),z=(2,-1,6,4),x+y=(-1,0,2,1)y+z=（-4,1,0,5）.\n   <br>\n   <br> (1)用字母表示你发现的规律；\n   <br>\n   <br> （2）求3x+2y=的值;\n  </div>\n </body>\n</html></div>", "1": "<div><p data-value=\"dc6ab6534c5941dbb5213ce9de21741a_242_0\" class=\"cut-check\">解下列方程组:</p><p data-value=\"dc6ab6534c5941dbb5213ce9de21741a_242_2\" class=\"cut-check\">(1)<img data-latex=\"%20%5Cbegin%7Bcases%7D3x%2By-4z%3D13%2C%5C%5C5x-y%2B3z%3D5%2C%5C%5Cx%2By-z%3D3%3B%5Cend%7Bcases%7D%20\" md=\"same\" src=\"https://bj.download.cycore.cn/question/2024/6/28/14/45/30abc7c8-fbb1-4856-9e73-9af6c6a51cb2.png\" type=\"ocr\" /></p><p data-value=\"dc6ab6534c5941dbb5213ce9de21741a_242_3\" class=\"cut-check\">(2)<img data-latex=\"%20%5Cbegin%7Bcases%7D3x-y%2Bz%3D4%2C%5C%5C2x%2B3y-z%3D12%2C%5C%5Cx%2By%2Bz%3D6.%5Cend%7Bcases%7D%20\" md=\"same\" src=\"https://bj.download.cycore.cn/question/2024/6/28/14/45/2ecf3641-47ab-45a5-b1d3-b0328be2fd4f.png\" type=\"ocr\" /></p></div>", "2": "<div><p>某商场计划用60000元从某厂家购进若干部新型手机，以满足市场需求．已知该厂家生产的甲、乙、丙三种型号手机，出厂价分别为每部1800元、600元和1200元．该商场用60000元恰好购买上述三种型号手机共40部，因市场需求甲型号手机比丙型号手机多购买了24部，求该商场购买了上述三种型号手机各多少部？</p></div>", "3": "<div><html>\n <head></head>\n <body>\n  <div class=\"ori-mathjax\">\n   <div class=\"\">\n    <p>解下列方程组</p>\n    <p>（1）$\\left \\{ {{\\begin{array}{ll} {x+y+z=3,} \\\\\\ {x+2y+3z=6,} \\\\\\ {2x+y+2z=5;} \\end{array}}} \\right .$&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</p>\n    <p>（2）$\\left \\{ {{\\begin{array}{ll} {x+2y=9,} \\\\\\ {y-3z=-5,} \\\\\\ {-x+5z=14.} \\end{array}}} \\right .$</p>\n   </div>\n  </div>\n </body>\n</html></div>"}, "ocrStrings": {"0": "\\{ ifly-latex-begin \\beginmatrix ifly-latex-end 5x+5y+10z=35,\\\\10y-30z=-70,\\\\10y-5z=0. ifly-latex-begin \\endmatrix ifly-latex-end  ifly-latex-begin \\textcircled{1} ifly-latex-end  ifly-latex-begin \\textcircled{4} ifly-latex-end  ifly-latex-begin \\textcircled{5} ifly-latex-end  ifly-latex-begin \\textcircled{5} ifly-latex-end + ifly-latex-begin \\textcircled{4} ifly-latex-end ,得\\{ ifly-latex-begin \\beginmatrix ifly-latex-end 5x+5y+10z=35,\\\\-10y-30z=-70,\\\\-35z=-7. ifly-latex-begin \\endmatrix ifly-latex-end  ifly-latex-begin \\textcircled{1} ifly-latex-end 你还有其他 ifly-latex-begin \\textcircled{4} ifly-latex-end 解法吗? ifly-latex-begin \\textcircled{6} ifly-latex-end 再通过回代,解得z=2,y=1,x=2.答:该食谱中包含A种食物2份,B种食物1份,C种食物2份.", "1": "1.解下列方程组:(1)\\{ ifly-latex-begin \\beginmatrix ifly-latex-end 3x+y-4z=13,\\\\5x-y+3z=5,\\\\x+y-z=3; ifly-latex-begin \\endmatrix ifly-latex-end (2)\\{ ifly-latex-begin \\beginmatrix ifly-latex-end 3x-y+z=4,\\\\2x+3y-z=12,\\\\x+y+z=6. ifly-latex-begin \\endmatrix ifly-latex-end ", "2": "2.某商场计划用60000元从某厂家购进若干部新型手机,以满足市场需求,已知该厂家生产的甲、乙、丙三种型号手机,出厂价分别为每部1800元、600元和1200元,该商场用60000元恰好购买上述三种型号手机共40部,因市场需求甲型号手机比丙型号机多购买了24部,求该商场购买了上述三种型号手机各多少部?", "3": "1.解下列方程组:(1)\\{ ifly-latex-begin \\beginmatrix ifly-latex-end x+y+z=3,\\\\x+2y+3z=6,\\\\2x+y+2z=5; ifly-latex-begin \\endmatrix ifly-latex-end (2)\\{ ifly-latex-begin \\beginmatrix ifly-latex-end x+2y=9,\\\\y-3z=-5,\\\\-x+5z=14; ifly-latex-begin \\endmatrix ifly-latex-end "}, "cfdsLevels": {"0": 0.3801652789115906, "1": 1, "2": 0.9928057789802551, "3": 1}}