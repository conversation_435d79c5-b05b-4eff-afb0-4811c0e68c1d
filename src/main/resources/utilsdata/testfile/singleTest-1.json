{"fullPageSearchRes": "", "userInfo": {"phaseCode": "03", "semester": "test", "bookCode": "19", "subjectCode": "02"}, "supportAnchors": [], "htmlStrings": {"hw-test@5d532529-77ca-4273-9394-328815d98873_0": "<div><p x=\"2\" y=\"5\" width=\"757\" height=\"49\" class=\"cut-check\" data-value=\"0\" style=\"\">1.在没有小括号的算式里,只有加减法或者只有乘除法,</p><p x=\"33\" y=\"62\" width=\"368\" height=\"51\" class=\"cut-check\" data-value=\"1\" style=\"\">要( )。</p></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_1": "<div><p x=\"0\" y=\"4\" width=\"759\" height=\"49\" class=\"cut-check\" data-value=\"0\" style=\"\">2.在没有小括号的算式里,有加减法,又有乘除法,要先</p><p x=\"31\" y=\"67\" width=\"447\" height=\"43\" class=\"cut-check\" data-value=\"1\" style=\"\">算( ),后算( )。</p></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_2": "<div><p x=\"2\" y=\"5\" width=\"660\" height=\"42\" class=\"cut-check\" data-value=\"0\" style=\"\">3.在有小括号的算式里,要先算( )。</p></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_3-0": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_3-1": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_3-2": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_3": "<div><p x=\"9\" y=\"3\" width=\"759\" height=\"48\" class=\"cut-check\" data-value=\"0\" style=\"\">4.把算式4<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/2a16f8ef-adf5-494d-a1ff-65614874c615.png\" data-latex=\"%5Ctimes\" w=\"15px\" h=\"15px\" />5=20,20+3=23合并成一个算式为</p><p x=\"44\" y=\"60\" width=\"271\" height=\"48\" class=\"cut-check\" data-value=\"1\" style=\"\">( )。</p></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_4-0": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_4": "<div><p x=\"6\" y=\"4\" width=\"757\" height=\"79\" class=\"cut-check\" data-value=\"0\" style=\"\">5.妈妈买回22个,小明吃了2个,把剩下的平均装</p><p x=\"38\" y=\"97\" width=\"445\" height=\"51\" class=\"cut-check\" data-value=\"1\" style=\"\">在4个盘子里,每盘装( )个。</p></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_5-0": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_5-1": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_5": "<div><p x=\"0\" y=\"4\" width=\"752\" height=\"49\" class=\"cut-check\" data-value=\"0\" style=\"\">6.18-9<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/12207519-12f2-47a7-800f-fd5d3b8c2e2b.png\" data-latex=\"%5Ctimes\" w=\"15px\" h=\"15px\" />2的计算结果( )(18-9)<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/12207519-12f2-47a7-800f-fd5d3b8c2e2b.png\" data-latex=\"%5Ctimes\" w=\"15px\" h=\"15px\" />2的计算</p><p x=\"43\" y=\"63\" width=\"478\" height=\"42\" class=\"cut-check\" data-value=\"1\" style=\"\">结果。(填“大于”“小于”或“等于”)</p></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_6": "<div><p x=\"0\" y=\"0\" width=\"887\" height=\"50\" class=\"cut-check\" data-value=\"0\" style=\"\">二、选一选。(把正确答案的序号填在括号里)(6分)</p></div><div>1.右边算盘上表示的数是( )。</div><div><div> <table><tbody><tr><td class=\"td50\">A. 111</td><td class=\"td50\">B. 555</td></tr><tr><td class=\"td50\">C. 666<img src=\"https://bj.download.cycore.cn/question/2022/12/30/18/17/93c67c55-fc9f-41ed-8163-a4a1aa6de2ee.jpg\" width=\"180px\" height=\"129px\" w=\"180px\" h=\"129px\" /></td></tr></tbody></table></div></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_7-0": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_7-1": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_7-2": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_7": "<div><p>2.3-个星期吃青草35千克, 每天吃青草</p><p>4千克,山羊比小鹿每天多吃青草多少千克?正确的</p><p>算式是( )。</p><p>[A]35-4</p><p>[B](35-4)<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/06e76c6f-43fc-4757-b293-1afa71763041.png\" data-latex=\"%5Cdiv\" w=\"15px\" h=\"15px\" />7[C]35<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/06e76c6f-43fc-4757-b293-1afa71763041.png\" data-latex=\"%5Cdiv\" w=\"15px\" h=\"15px\" />7-4</p></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_8-0": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_8-1": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_8-2": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_8": "<div><p>3.算式36-8<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/a05b388d-e3c1-4b6b-802e-03820c930abd.png\" data-latex=\"%5Cdiv\" w=\"15px\" h=\"15px\" />4与(36-8)<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/a05b388d-e3c1-4b6b-802e-03820c930abd.png\" data-latex=\"%5Cdiv\" w=\"15px\" h=\"15px\" />4的大小关系是( )。</p><p>[A]后者大</p><p>[B]前者大</p><p>[C]相等</p></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_9-0": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_9-1": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_9-2": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_9": "<div><p>4.“8乘69减去60的差,积是多少”列式正确的是</p><p>( )。</p><p>[A]8<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/122a6bdb-41b9-4abe-999f-958b65df8ba7.png\" data-latex=\"%5Ctimes\" w=\"15px\" h=\"15px\" />69-60</p><p>[B]8<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/122a6bdb-41b9-4abe-999f-958b65df8ba7.png\" data-latex=\"%5Ctimes\" w=\"15px\" h=\"15px\" />(69-60)[C]60<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/122a6bdb-41b9-4abe-999f-958b65df8ba7.png\" data-latex=\"%5Ctimes\" w=\"15px\" h=\"15px\" />8-69</p></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_10-0": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_10": "<div><p>5.计算(16+24)<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/03d2de8d-f989-420b-bba7-904027f7c91e.png\" data-latex=\"%5Cdiv\" w=\"15px\" h=\"15px\" />8的结果是( )。</p><p>[A]5</p><p>[B]19</p><p>[C]26</p></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_11-0": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_11-1": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_11-2": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_11-3": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_11": "<div><p>6.要使30<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/a08c3a19-97e6-48f3-aacc-c35248ec319d.png\" data-latex=\"%5Cdiv\" w=\"15px\" h=\"15px\" />6-3的计算结果是10,可以使用小括号,原</p><p>式变为( )。</p><p>[A](30<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/a08c3a19-97e6-48f3-aacc-c35248ec319d.png\" data-latex=\"%5Cdiv\" w=\"15px\" h=\"15px\" />6)-3[B](30-3)<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/a08c3a19-97e6-48f3-aacc-c35248ec319d.png\" data-latex=\"%5Cdiv\" w=\"15px\" h=\"15px\" />6[C]30<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/a08c3a19-97e6-48f3-aacc-c35248ec319d.png\" data-latex=\"%5Cdiv\" w=\"15px\" h=\"15px\" />(6-3)</p></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_12": "<div><p x=\"0\" y=\"6\" width=\"299\" height=\"48\" class=\"cut-check\" data-value=\"0\" style=\"\">三、数学诊所。(9分)</p><p x=\"16\" y=\"87\" width=\"170\" height=\"197\" class=\"cut-check\" data-value=\"1\" style=\"\"><img src=\"https://bj.download.cycore.cn/question/2022/8/11/21/8/92179a55-484c-4438-8557-fdf22d30d159.jpg\" width=\"170px\" height=\"197px\" w=\"170px\" h=\"197px\" /></p><p x=\"274\" y=\"85\" width=\"169\" height=\"197\" class=\"cut-check\" data-value=\"2\" style=\"\"><img src=\"https://bj.download.cycore.cn/question/2022/8/11/21/8/ff1e14a8-8691-41a1-9b04-9207290d64f0.jpg\" width=\"169px\" height=\"197px\" w=\"169px\" h=\"197px\" /></p><p x=\"522\" y=\"77\" width=\"198\" height=\"201\" class=\"cut-check\" data-value=\"3\" style=\"\"><img src=\"https://bj.download.cycore.cn/question/2022/8/11/21/8/9a262007-f43e-4467-8097-103430c2af81.jpg\" width=\"180px\" height=\"182px\" w=\"180px\" h=\"182px\" /></p><p x=\"33\" y=\"293\" width=\"81\" height=\"41\" class=\"cut-check\" data-value=\"4\" style=\"\">改正:</p><p x=\"301\" y=\"290\" width=\"80\" height=\"42\" class=\"cut-check\" data-value=\"5\" style=\"\">改正:</p><p x=\"569\" y=\"287\" width=\"80\" height=\"41\" class=\"cut-check\" data-value=\"6\" style=\"\">改正:</p></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_13-0": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_13": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_14-0": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_14-1": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_14-2": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_14-3": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_14-4": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_14": "<div><p x=\"5\" y=\"3\" width=\"371\" height=\"47\" class=\"cut-check\" data-value=\"0\" style=\"\">四、摘苹果, 连一连。(5分)</p><p x=\"9\" y=\"84\" width=\"133\" height=\"126\" class=\"cut-check\" data-value=\"1\" style=\"\"><img src=\"https://bj.download.cycore.cn/question/2022/8/15/15/22/b51421c7-6c64-4e74-9653-7125a4fb615f.jpg\" /></p><p x=\"158\" y=\"83\" width=\"137\" height=\"125\" class=\"cut-check\" data-value=\"2\" style=\"\"><img src=\"https://bj.download.cycore.cn/question/2022/8/15/15/22/94b0b971-49d3-410f-9424-e3534acaed6f.jpg\" /></p><p x=\"305\" y=\"80\" width=\"286\" height=\"127\" class=\"cut-check\" data-value=\"3\" style=\"\"><img src=\"https://bj.download.cycore.cn/question/2022/8/15/15/22/ba120e76-fd4b-47f5-a874-c1b42943ed95.jpg\" /></p><p x=\"610\" y=\"79\" width=\"139\" height=\"125\" class=\"cut-check\" data-value=\"4\" style=\"\"><img src=\"https://bj.download.cycore.cn/question/2022/8/15/15/22/f6036bff-c697-4d12-9a90-124be8f83d63.jpg\" /></p><p x=\"19\" y=\"373\" width=\"115\" height=\"120\" class=\"cut-check\" data-value=\"5\" style=\"\"><img src=\"https://bj.download.cycore.cn/question/2022/8/15/15/22/1261e7a9-af62-4047-9ae8-efa079e91c12.jpg\" /></p><p x=\"171\" y=\"371\" width=\"115\" height=\"120\" class=\"cut-check\" data-value=\"6\" style=\"\"><img src=\"https://bj.download.cycore.cn/question/2022/8/15/15/22/d19ca362-7772-4790-8a79-49711f5efa69.jpg\" /></p><p x=\"319\" y=\"370\" width=\"116\" height=\"119\" class=\"cut-check\" data-value=\"7\" style=\"\"><img src=\"https://bj.download.cycore.cn/question/2022/8/15/15/22/3f171015-54dd-495b-a6ee-a976d985c6f9.jpg\" /></p><p x=\"469\" y=\"368\" width=\"114\" height=\"120\" class=\"cut-check\" data-value=\"8\" style=\"\"><img src=\"https://bj.download.cycore.cn/question/2022/8/15/15/22/45a25252-c2c3-45ad-88d8-3677cc77d5e1.jpg\" /></p><p x=\"623\" y=\"366\" width=\"115\" height=\"121\" class=\"cut-check\" data-value=\"9\" style=\"\"><img src=\"https://bj.download.cycore.cn/question/2022/8/15/15/22/4553329a-5658-401a-a1e9-b543459a20a7.jpg\" /></p></div>", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-0": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-1": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-2": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-3": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-4": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-5": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-6": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-7": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-8": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-9": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_15": "<div><p x=\"4\" y=\"11\" width=\"281\" height=\"47\" class=\"cut-check\" data-value=\"0\" style=\"\">五、计算题。(18分)</p><p x=\"37\" y=\"88\" width=\"152\" height=\"40\" class=\"cut-check\" data-value=\"1\" style=\"\">36-16<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/45cbff28-8993-4637-8fc2-c6a836f0387f.png\" data-latex=\"%5Cdiv\" w=\"15px\" h=\"15px\" />4</p><p x=\"283\" y=\"85\" width=\"195\" height=\"41\" class=\"cut-check\" data-value=\"2\" style=\"\">40<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/45cbff28-8993-4637-8fc2-c6a836f0387f.png\" data-latex=\"%5Cdiv\" w=\"15px\" h=\"15px\" />(52-44)</p><p x=\"560\" y=\"81\" width=\"165\" height=\"41\" class=\"cut-check\" data-value=\"3\" style=\"\">63-15+29</p><p x=\"9\" y=\"166\" width=\"37\" height=\"37\" class=\"cut-check\" data-value=\"4\" style=\"\">=</p><p x=\"255\" y=\"166\" width=\"38\" height=\"31\" class=\"cut-check\" data-value=\"5\" style=\"\">=</p><p x=\"9\" y=\"241\" width=\"38\" height=\"38\" class=\"cut-check\" data-value=\"6\" style=\"\">=</p><p x=\"255\" y=\"240\" width=\"39\" height=\"32\" class=\"cut-check\" data-value=\"7\" style=\"\">=</p><p x=\"39\" y=\"382\" width=\"135\" height=\"39\" class=\"cut-check\" data-value=\"8\" style=\"\">7<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/ab61cb05-f839-48db-9661-530eb53cdedf.png\" data-latex=\"%5Ctimes\" w=\"15px\" h=\"15px\" />8-25</p><p x=\"286\" y=\"379\" width=\"136\" height=\"41\" class=\"cut-check\" data-value=\"9\" style=\"\">19+9<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/ab61cb05-f839-48db-9661-530eb53cdedf.png\" data-latex=\"%5Ctimes\" w=\"15px\" h=\"15px\" />7</p><p x=\"565\" y=\"376\" width=\"179\" height=\"42\" class=\"cut-check\" data-value=\"10\" style=\"\">(38+18)<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/45cbff28-8993-4637-8fc2-c6a836f0387f.png\" data-latex=\"%5Cdiv\" w=\"15px\" h=\"15px\" />7</p><p x=\"11\" y=\"463\" width=\"38\" height=\"36\" class=\"cut-check\" data-value=\"11\" style=\"\">=</p><p x=\"257\" y=\"461\" width=\"38\" height=\"32\" class=\"cut-check\" data-value=\"12\" style=\"\">=</p><p x=\"11\" y=\"536\" width=\"39\" height=\"36\" class=\"cut-check\" data-value=\"13\" style=\"\">=</p><p x=\"258\" y=\"535\" width=\"39\" height=\"31\" class=\"cut-check\" data-value=\"14\" style=\"\">=</p><p x=\"41\" y=\"676\" width=\"165\" height=\"41\" class=\"cut-check\" data-value=\"15\" style=\"\">72<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/45cbff28-8993-4637-8fc2-c6a836f0387f.png\" data-latex=\"%5Cdiv\" w=\"15px\" h=\"15px\" />(4+5)</p><p x=\"288\" y=\"673\" width=\"180\" height=\"42\" class=\"cut-check\" data-value=\"16\" style=\"\">6<img src=\"https://bj.download.cycore.cn/question/2022/8/17/17/18/ab61cb05-f839-48db-9661-530eb53cdedf.png\" data-latex=\"%5Ctimes\" w=\"15px\" h=\"15px\" />(34-29)</p><p x=\"565\" y=\"670\" width=\"165\" height=\"41\" class=\"cut-check\" data-value=\"17\" style=\"\">50-28+17</p><p x=\"12\" y=\"758\" width=\"39\" height=\"34\" class=\"cut-check\" data-value=\"18\" style=\"\">=</p><p x=\"261\" y=\"754\" width=\"37\" height=\"37\" class=\"cut-check\" data-value=\"19\" style=\"\">=</p><p x=\"13\" y=\"829\" width=\"39\" height=\"37\" class=\"cut-check\" data-value=\"20\" style=\"\">=</p><p x=\"261\" y=\"828\" width=\"39\" height=\"36\" class=\"cut-check\" data-value=\"21\" style=\"\">=</p></div>"}, "ocrStrings": {"hw-test@5d532529-77ca-4273-9394-328815d98873_0": "3.在有小括号的算式里,要先算()", "hw-test@5d532529-77ca-4273-9394-328815d98873_1": "3.在有小括号的算式里,要先算()", "hw-test@5d532529-77ca-4273-9394-328815d98873_2": "3.在有小括号的算式里,要先算()", "hw-test@5d532529-77ca-4273-9394-328815d98873_3-0": "4 \\times 5 = 2 0", "hw-test@5d532529-77ca-4273-9394-328815d98873_3-1": "4 \\times 5 + 3", "hw-test@5d532529-77ca-4273-9394-328815d98873_3-2": "2 0 + 3 = 2 3", "hw-test@5d532529-77ca-4273-9394-328815d98873_3": "5.妈妈买回22个,小明吃了2个,把剩下的平均装在4个盘子里,每盘装()个。", "hw-test@5d532529-77ca-4273-9394-328815d98873_4-0": "2 2 - 2 = 2 0", "hw-test@5d532529-77ca-4273-9394-328815d98873_4": "5.妈妈买回22个,小明吃了2个,把剩下的平均装在4个盘子里,每盘装()个。", "hw-test@5d532529-77ca-4273-9394-328815d98873_5-0": "1 8 - 9 \\times 2", "hw-test@5d532529-77ca-4273-9394-328815d98873_5-1": "( 1 8 - 9 ) \\times 2", "hw-test@5d532529-77ca-4273-9394-328815d98873_5": "6.18-9 ifly-latex-begin \\times ifly-latex-end 2的计算结果()(18-9) ifly-latex-begin \\times ifly-latex-end 2的计算结果。(填\"大于\"\"小于\"或\"等子\")", "hw-test@5d532529-77ca-4273-9394-328815d98873_6": "二、选择题。(将正确答案的序号涂一涂)(6分)1.算式60+5 ifly-latex-begin \\times ifly-latex-end 7= ifly-latex-begin \\boxed{} ifly-latex-end ,应先算 ifly-latex-begin \\boxed{} ifly-latex-end 。(A)加法(B)乘法1c1从左到右依次算", "hw-test@5d532529-77ca-4273-9394-328815d98873_7-0": "[ A 1 3 5 - 4", "hw-test@5d532529-77ca-4273-9394-328815d98873_7-1": "( 3 5 - 4 ) \\div 7", "hw-test@5d532529-77ca-4273-9394-328815d98873_7-2": "1 3 5 \\div 7 - 4", "hw-test@5d532529-77ca-4273-9394-328815d98873_7": "一个星期吃青草35千克,每天吃青草2.4千克,山羊比小鹿每天多吃青草多少千克?正确的算式是\\unk)。[A]35-4(B)(35-4) ifly-latex-begin \\div ifly-latex-end 7 ifly-latex-begin \\space ifly-latex-end |C135 ifly-latex-begin \\div ifly-latex-end 7-4", "hw-test@5d532529-77ca-4273-9394-328815d98873_8-0": "3 6 - 8 \\div 4", "hw-test@5d532529-77ca-4273-9394-328815d98873_8-1": "( 3 6 - 8 ) \\div 4", "hw-test@5d532529-77ca-4273-9394-328815d98873_8-2": "2 8 \\div 4", "hw-test@5d532529-77ca-4273-9394-328815d98873_8": "5.计算(16+24) ifly-latex-begin \\div ifly-latex-end 8的结果是()[A]5[B]19|C|26", "hw-test@5d532529-77ca-4273-9394-328815d98873_9-0": "1 A 1 8 \\times 6 9 - 6 0", "hw-test@5d532529-77ca-4273-9394-328815d98873_9-1": "1 8 \\times ( 6 9 - 6 0 )", "hw-test@5d532529-77ca-4273-9394-328815d98873_9-2": "1 6 0 \\times 8 - 6 9", "hw-test@5d532529-77ca-4273-9394-328815d98873_9": "4.\"8乘69减去60的差,积是多少\"列式正确的是 ifly-latex-begin \\bigcirc ifly-latex-end ).1A18 ifly-latex-begin \\times ifly-latex-end 69-60[B18 ifly-latex-begin \\times ifly-latex-end (69-60)]16160 ifly-latex-begin \\times ifly-latex-end 8-69", "hw-test@5d532529-77ca-4273-9394-328815d98873_10-0": "( 1 6 + 2 4 ) \\div 8", "hw-test@5d532529-77ca-4273-9394-328815d98873_10": "5.计算(16+24) ifly-latex-begin \\div ifly-latex-end 8的结果是()[A]5[B]19|C|26", "hw-test@5d532529-77ca-4273-9394-328815d98873_11-0": "3 0 \\div 6 - 3", "hw-test@5d532529-77ca-4273-9394-328815d98873_11-1": "[ A ] ( 3 0 \\div 6 ) - 3", "hw-test@5d532529-77ca-4273-9394-328815d98873_11-2": "( 3 0 - 3 ) \\div 6", "hw-test@5d532529-77ca-4273-9394-328815d98873_11-3": "1 3 0 \\div ( 6 - 3 )", "hw-test@5d532529-77ca-4273-9394-328815d98873_11": "6.要使30 ifly-latex-begin \\div ifly-latex-end 6-3的计算结果是10,可以使用小括号,原式变为( ifly-latex-begin \\square ifly-latex-end [A1(30 ifly-latex-begin \\div ifly-latex-end 6)-3-[B](30-3) ifly-latex-begin \\div ifly-latex-end 6 ifly-latex-begin \\space[C] ifly-latex-end 30 ifly-latex-begin \\div ifly-latex-end (6-3)", "hw-test@5d532529-77ca-4273-9394-328815d98873_12": "五、计算题。(18分)36-16 ifly-latex-begin \\div ifly-latex-end 440 ifly-latex-begin \\div ifly-latex-end (52-44)63-15+29 ifly-latex-begin \\underline{} ifly-latex-end 100==== ifly-latex-begin \\square ifly-latex-end 7 ifly-latex-begin \\times ifly-latex-end 8-2519 ifly-latex-begin \\div ifly-latex-end 9 ifly-latex-begin \\times ifly-latex-end 7(38+18) ifly-latex-begin \\div ifly-latex-end 7=1000== ifly-latex-begin \\square ifly-latex-end C.=50-28+1772 ifly-latex-begin \\div ifly-latex-end (4+5)6 ifly-latex-begin \\times ifly-latex-end (34-29)()=", "hw-test@5d532529-77ca-4273-9394-328815d98873_13-0": "\\beginsplit 5 1 \\\\ - 3 6 \\\\ \\hline 5 \\endsplit", "hw-test@5d532529-77ca-4273-9394-328815d98873_13": "", "hw-test@5d532529-77ca-4273-9394-328815d98873_14-0": "( 3 8 + 1 6 ) \\div 9", "hw-test@5d532529-77ca-4273-9394-328815d98873_14-1": "5 1 - 9 \\times 4", "hw-test@5d532529-77ca-4273-9394-328815d98873_14-2": "8 \\times ( 7 + 2 )", "hw-test@5d532529-77ca-4273-9394-328815d98873_14-3": "6 8 \\div 4 \\times 8", "hw-test@5d532529-77ca-4273-9394-328815d98873_14-4": "4 2 \\div ( 5 3 - 4 7 )", "hw-test@5d532529-77ca-4273-9394-328815d98873_14": "五、计算题。(18分)36-16 ifly-latex-begin \\div ifly-latex-end 440 ifly-latex-begin \\div ifly-latex-end (52-44)63-15+29 ifly-latex-begin \\underline{} ifly-latex-end 100==== ifly-latex-begin \\square ifly-latex-end 7 ifly-latex-begin \\times ifly-latex-end 8-2519 ifly-latex-begin \\div ifly-latex-end 9 ifly-latex-begin \\times ifly-latex-end 7(38+18) ifly-latex-begin \\div ifly-latex-end 7=1000== ifly-latex-begin \\square ifly-latex-end C.=50-28+1772 ifly-latex-begin \\div ifly-latex-end (4+5)6 ifly-latex-begin \\times ifly-latex-end (34-29)()=", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-0": "\\beginsplit&3 6 - 1 6 \\div 4 \\\\&3 6 - 4 \\\\&= 3 2\\\\\\endsplit", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-1": "\\beginsplit&7 \\times 8 - 2 5 \\\\&= 5 6 - 2 5 \\\\&= 3 1\\\\\\endsplit", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-2": "\\beginsplit&7 2 \\div ( 4 + 5 ) \\\\&= 7 2 \\div 9 \\\\&= 8\\\\\\endsplit", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-3": "\\beginsplit&4 0 \\div ( 5 2 - 4 4 ) \\\\&= 4 0 \\div 8 \\\\&= 5\\\\\\endsplit", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-4": "\\beginsplit&1 9 + 9 \\times 7 \\\\&= 1 9 + 6 3 \\\\&= 8 2\\\\\\endsplit", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-5": "\\beginsplit&6 \\times ( 1 - 2 9 ) \\\\&= 6 \\times 5 \\\\&= 3 0\\\\\\endsplit", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-6": "\\beginsplit&6 3 - 1 5 + 2 9 \\\\&= 6 3 - 4 4 \\\\&= 1 9\\\\\\endsplit", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-7": "\\beginsplit&( 3 8 + 1 8 ) \\div 7 \\\\&= 5 6 \\div 7 \\\\&= 8\\\\\\endsplit", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-8": "\\beginsplit&5 0 - 2 8 = 1 7 \\\\&- 5 0 - 4 5 \\\\&- 5\\\\\\endsplit", "hw-test@5d532529-77ca-4273-9394-328815d98873_15-9": "\\beginsplit&5 - 2 8 + 1 7 \\\\&= 1 2 + 1 7 \\\\&= 2 9\\\\\\endsplit", "hw-test@5d532529-77ca-4273-9394-328815d98873_15": "五、计算题。(18分)36-16 ifly-latex-begin \\div ifly-latex-end 440 ifly-latex-begin \\div ifly-latex-end (52-44)63-15+29 ifly-latex-begin \\underline{} ifly-latex-end 100==== ifly-latex-begin \\square ifly-latex-end 7 ifly-latex-begin \\times ifly-latex-end 8-2519 ifly-latex-begin \\div ifly-latex-end 9 ifly-latex-begin \\times ifly-latex-end 7(38+18) ifly-latex-begin \\div ifly-latex-end 7=1000== ifly-latex-begin \\square ifly-latex-end C.=50-28+1772 ifly-latex-begin \\div ifly-latex-end (4+5)6 ifly-latex-begin \\times ifly-latex-end (34-29)()="}}