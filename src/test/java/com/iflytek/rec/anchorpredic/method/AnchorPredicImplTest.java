package com.iflytek.rec.anchorpredic.method;

import ai.djl.modality.nlp.bert.BertToken;
import ai.djl.modality.nlp.bert.BertTokenizer;
import com.iflytek.infer.IInferHelper;
import com.iflytek.infer.interfaces.impl.InferHelperImpl;
import com.iflytek.infer.interfaces.params.InferParam;
import com.iflytek.infer.interfaces.params.InferResult;
import com.iflytek.rec.anchorpredic.utils.BertFullTokenizer;
import com.iflytek.rec.anchorpredic.impl.AnchorPredicMethodImpl;
import com.iflytek.rec.anchorpredic.interfaces.IAnchorPredictMethod;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;


/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/9/26 15:52
 */
public class AnchorPredicImplTest {

    private static final Logger logger = LoggerFactory.getLogger(AnchorPredicImplTest.class);

    IAnchorPredictMethod anchorPredic = new AnchorPredicMethodImpl();

//    @Test
//    public void dataAnalysisExceptLatexTest() throws Exception {
//        List<String> input = FileReaderUtil.of().FileReader("topic_test.html");
//        for (String in : input) {
//            String sb1 = anchorPredic.dataAnalysisExceptLatex(in);
//            System.out.println(sb1);
//        }
//    }

//    @Test
//    public void dataAnalysisIncludeLatexTest() throws IOException {
//        List<String> input = FileReaderUtil.of().FileReader("topic_test.html");
//        int i = 0;
//        for (String contentLine : input) {
//            i++;
//            System.out.println("^^^^^^^^^^^^^^^^^CASE： " + i);
//            System.out.println("入参：" + contentLine + "\n");
//            String sb = anchorPredic.dataAnalysisExceptLatex(contentLine);
//            System.out.println("出参[未解析latex]：" + sb + "\n");
//            String sb1 = anchorPredic.dataAnalysisIncludeLatex(contentLine);
//            System.out.println("出参[解析latex]：" + sb1 + "\n");
//            String sb2 = anchorPredic.dataAnalysisIncludeLatexUMap(contentLine);
//            System.out.println("出参[解析latex使用map映射表]：" + sb2 + "\n");
//            System.out.println("END----------------" + "\n");
//        }
//    }

    @Test
    public void dataAnalysisOcr() {
        IAnchorPredictMethod iAnchorPredictMethod = new AnchorPredicMethodImpl();
        String in = "8.6+1.9=10.5(元) ifly-latex-begin \\enter ifly-latex-end 10.5+8.6=19.1(元)";
        System.out.println(iAnchorPredictMethod.dataAnalysisOcr(in,"02_03"));
        logger.info(iAnchorPredictMethod.dataAnalysisOcr(in,"02_03"));
    }

    @Test
    public void testBertTokenizer() {
        logger.info("testBertTokenizer:");
        String question = "人教版人教版人教版人教版人教版人教版人教版";
//        String paragraph = "Radio International was a general entertainment Channel. Which operated between December 1983 and April 2001";
        BertTokenizer tokenizer = new BertTokenizer();
        String book = "人教版";
        BertToken bertToken = tokenizer.encode(book, question);
        logger.info("Tokens: {}", bertToken.getTokens());
        logger.info("TokenTypes: {}", bertToken.getTokenTypes());
        logger.info("AttentionMask: {}", bertToken.getAttentionMask());
    }

    @Test
    public void testBertFullTokenizer() {

//        String input = "This tokenizer generates tokens for input sentence";
        String input = "人教版人教版人教版人教版人教版人教版";
//        String input = "人教版人教版人教版人教版人教版人###教版test name ## yangyang guang zoomzeroone two three four";
        List<String> tokens = BertFullTokenizer.getBertFullTokenizer(input,false);
        List<Long> tokenIndex = BertFullTokenizer.getTokensIndex(tokens);


    }

    @Test
    public void testModel() {
        logger.info("冒烟测试");
        IInferHelper inferHelper = InferHelperImpl.instance("");
        inferHelper.init("./engine.cfg");
        InferParam inputs = new InferParam();
        try {
            InferResult res = inferHelper.predict("key", inputs);
            logger.info("推理结果:{}", res);
        } catch (Exception e) {
            logger.error(String.valueOf(e));
            e.printStackTrace();
        }
        inferHelper.fini();
    }

    @Test
    public void sectest() {
        for (int i = 0; i < 10; i++) {
            Instant time1 = Instant.now();
            String s = "zheshiyigecehsi223";
            for (int k = 0; k < 2000; k++) {

                s.replace("223", "445");
                s.replace("445", "223");
            }
            logger.info(s);
            Instant time2 = Instant.now();
            logger.info("时间 cost= {} ms", ChronoUnit.MILLIS.between(time1, time2));

        }
    }
}
