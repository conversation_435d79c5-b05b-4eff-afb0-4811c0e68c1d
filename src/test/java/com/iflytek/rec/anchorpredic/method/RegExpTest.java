package com.iflytek.rec.anchorpredic.method;

import com.iflytek.rec.anchorpredic.impl.AnchorPredicMethodImpl;
import com.iflytek.rec.anchorpredic.impl.AnchorPredictEngineServiceImpl;
import com.iflytek.rec.anchorpredic.interfaces.IAnchorPredictMethod;
import com.iflytek.rec.anchorpredic.method.paramAnalysis.ParamAnalysisHolder;
import com.iflytek.rec.anchorpredic.pojo.AnchorPredictParams;
import com.iflytek.rec.anchorpredic.pojo.BookAnchorMap;
import com.iflytek.rec.anchorpredic.pojo.UserInfo;
import com.iflytek.rec.anchorpredic.utils.Constant;
import com.iflytek.rec.anchorpredic.datafactory.holder.FileReaderUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.io.IOException;
import java.util.*;

import static com.iflytek.rec.anchorpredic.datafactory.holder.FileReaderUtil.supportAnchors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/12/8 13:56
 */
public class RegExpTest {
    static IAnchorPredictMethod iAnchorPredictMethod = new AnchorPredicMethodImpl();

    private static List<AnchorPredictParams> initparms(String fileName) throws Exception {
        List<AnchorPredictParams> anchorPredictParams;
        if (StringUtils.equals(fileName, "sig")) {
            anchorPredictParams = FileReaderUtil.consistenceTestSigle();
        } else if (StringUtils.equals(fileName, "all")) {
            anchorPredictParams = FileReaderUtil.consistenceTestAll();
        } else {
            anchorPredictParams = FileReaderUtil.consistenceJuniorTestAll();
        }
        String resurcePattern = "02_03";
        if (!anchorPredictParams.isEmpty()) {
            UserInfo userInfo = anchorPredictParams.get(0).getUserInfo();
            resurcePattern = userInfo.getSubjectCode() + "_" + userInfo.getPhaseCode();
        }

        List<BookAnchorMap> anchorMaps = supportAnchors(resurcePattern);
        Map<String, List<String>> bookanchormap = new HashMap<>();
        for (BookAnchorMap bookAnchorMap : anchorMaps) {
            bookanchormap.put(bookAnchorMap.getBookV(), bookAnchorMap.getSupporanhcors());
        }
        for (AnchorPredictParams anchorPredictParams1 : anchorPredictParams) {
            anchorPredictParams1.setSupportAnchors(bookanchormap.get(anchorPredictParams1.getUserInfo().getBookCode()));
        }
        return anchorPredictParams;
    }

    @Test
    public void compareTest() {
        String tr1 = "08980f53b43a9-2392-4aa0-9a33-b2cbd6515265";
        String tr2 = "08981f53b43a9-2392-4aa0-9a33-b2cbd6515265";
        System.out.println(tr1.compareTo(tr2));

    }

    @Test
    public void regTest() throws IOException {
        List<String> list = new ArrayList<>();
        list.add("<div><p x=\"9\" y=\"34\" width=\"100\" height=\"75\" class=\"cut-check\" data-value=\"0\" style=\"\">(2)</p><p x=\"440\" y=\"19\" width=\"289\" height=\"90\" class=\"cut-check\" data-value=\"1\" style=\"\">230<img src=\"https://www.zhixue.com/latexservice/toPng?%5Ctimes\" data-latex=\"%5Ctimes\">3=<img src=\"https://www.zhixue.com/latexservice/toPng?%5Csquare\" data-latex=\"%5Csquare\"></p><p x=\"1345\" y=\"19\" width=\"270\" height=\"90\" class=\"cut-check\" data-value=\"2\" style=\"\">108<img src=\"https://www.zhixue.com/latexservice/toPng?%5Ctimes\" data-latex=\"%5Ctimes\">7=<img src=\"https://www.zhixue.com/latexservice/toPng?%5Csquare\" data-latex=\"%5Csquare\"></p><table border='1' cellspacing='0' style='text-align:center'><tr><td rowspan='1' colspan='1' width='57'><img src=\"https://www.zhixue.com/latexservice/toPng?%5Ctimes\" data-latex=\"%5Ctimes\"></td><td rowspan='1' colspan='1' width='57'>OO</td><td rowspan='1' colspan='1' width='57'>30</td></tr><tr><td rowspan='1' colspan='1' width='57'>3</td><td rowspan='1' colspan='1' width='57'></td><td rowspan='1' colspan='1' width='57'></td></tr></table><p x=\"1297\" y=\"331\" width=\"98\" height=\"32\" class=\"cut-check\" data-value=\"4\" style=\"\"><img src='https://bj.download.cycore.cn/question/2024/1/2/10/46/80bdf27c-29da-491d-b151-d3267d6607ab.jpg' width='98px' height='32px' w='98px' h='32px'></p><p x=\"387\" y=\"382\" width=\"191\" height=\"94\" class=\"cut-check\" data-value=\"5\" style=\"\"><img src=\"https://www.zhixue.com/latexservice/toPng?%5Csquare\" data-latex=\"%5Csquare\">+- -</p><p x=\"507\" y=\"384\" width=\"117\" height=\"80\" class=\"cut-check\" data-value=\"6\" style=\"\"><img src='https://bj.download.cycore.cn/question/2024/1/2/10/46/e74b3435-6a6d-4ee8-b1df-0bc65f581b93.jpg' width='117px' height='80px' w='117px' h='80px'></p><p x=\"578\" y=\"384\" width=\"204\" height=\"92\" class=\"cut-check\" data-value=\"7\" style=\"\">]=<img src=\"https://www.zhixue.com/latexservice/toPng?%5Csquare\" data-latex=\"%5Csquare\"></p><p x=\"1288\" y=\"382\" width=\"176\" height=\"94\" class=\"cut-check\" data-value=\"8\" style=\"\"><img src=\"https://www.zhixue.com/latexservice/toPng?%5Csquare\" data-latex=\"%5Csquare\">+[</p><p x=\"1400\" y=\"384\" width=\"126\" height=\"80\" class=\"cut-check\" data-value=\"9\" style=\"\"><img src='https://bj.download.cycore.cn/question/2024/1/2/10/46/a4ddd1a2-2dbb-4af2-8b8e-f4c5e56425e4.jpg' width='126px' height='80px' w='126px' h='80px'></p><p x=\"1463\" y=\"384\" width=\"218\" height=\"92\" class=\"cut-check\" data-value=\"10\" style=\"\"><img src=\"https://www.zhixue.com/latexservice/toPng?%5Crightarrow\" data-latex=\"%5Crightarrow\">=<img src=\"https://www.zhixue.com/latexservice/toPng?%5Csquare\" data-latex=\"%5Csquare\"></p></div>");

        for (String s : list) {
            testHtml(s,"");
        }
    }

    @Test
    public void singTest() throws IOException {

        List<String> list = new ArrayList<>();
//        list.add("6二进制是电子计算机中广泛采用的一种数制。二进制数据是用0和1两个数字来表示的数,它的进位规则是\"逢二进一\"。找一找十进制数与二进制数之间的联系与规律,并填一填。十进制数 ifly-latex-begin \\space ifly-latex-end 1 ifly-latex-begin \\space ifly-latex-end 2 ifly-latex-begin \\space ifly-latex-end 3 ifly-latex-begin \\space ifly-latex-end 4 ifly-latex-begin \\space ifly-latex-end 5 ifly-latex-begin \\space ifly-latex-end 6 ifly-latex-begin \\space ifly-latex-end 7 ifly-latex-begin \\space ifly-latex-end 89二进制数 ifly-latex-begin \\space ifly-latex-end 1 ifly-latex-begin \\space ifly-latex-end 10 ifly-latex-begin \\space ifly-latex-end 11 ifly-latex-begin \\space ifly-latex-end 100 ifly-latex-begin \\space ifly-latex-end 101 ifly-latex-begin \\space ifly-latex-end () ifly-latex-begin \\space ifly-latex-end 1111000 ifly-latex-begin \\space ifly-latex-end ( ifly-latex-begin \\space ifly-latex-end )");
//        list.add("3. ifly-latex-begin \\frac{1}{4} ifly-latex-end + ifly-latex-begin \\frac{1}{6} ifly-latex-end =( ifly-latex-begin \\smear ifly-latex-end )+( ifly-latex-begin \\smear ifly-latex-end )=()");
//        list.add("3. ifly-latex-begin \\frac{1}{4} ifly-latex-end + ifly-latex-begin \\frac{1}{6} ifly-latex-end =( ifly-latex-begin \\smear ifly-latex-end )+( ifly-latex-begin \\smear ifly-latex-end )=()");
//        list.add("3. ifly-latex-begin \\frac{1}{4} ifly-latex-end + ifly-latex-begin \\frac{1}{6} ifly-latex-end  ( ifly-latex-begin \\smear ifly-latex-end ) ( ifly-latex-begin \\smear ifly-latex-end )=()");
//        list.add("\\frac { ( 1 5 ) } { 3 5 } + \\frac { ( 2 8 ) } { 3 5 }");
//        list.add( "6、学校运动会的领奖台除了底面不涂漆外,其他各面都涂漆,需要涂漆的面积是多少平方厘米?(单位:cm)4014cm23.03100100100");
//        list.add("\\frac { 1 3 } { 1 7 } \\times \\frac { \\boxed { 3 } } { 4 } < \\frac { 1 3 } { 1 7 }");
        list.add("2.算一算。 ifly-latex-begin \\frac{4}{15} ifly-latex-end  ifly-latex-begin \\times ifly-latex-end  ifly-latex-begin \\frac{5}{12} ifly-latex-end  ifly-latex-begin \\frac{1}{3} ifly-latex-end  ifly-latex-begin \\times ifly-latex-end  ifly-latex-begin \\frac{5}{4} ifly-latex-end = ifly-latex-begin \\frac{3}{2} ifly-latex-end  ifly-latex-begin \\times ifly-latex-end  ifly-latex-begin \\frac{8}{9} ifly-latex-end = ifly-latex-begin \\frac{8}{27} ifly-latex-end  ifly-latex-begin \\times ifly-latex-end  ifly-latex-begin \\frac{9}{10} ifly-latex-end = ifly-latex-begin \\frac{13}{15} ifly-latex-end  ifly-latex-begin \\times ifly-latex-end  ifly-latex-begin \\frac{3}{2} ifly-latex-end = ifly-latex-begin \\frac{7}{12} ifly-latex-end  ifly-latex-begin \\times ifly-latex-end  ifly-latex-begin \\frac{3}{14} ifly-latex-end = ifly-latex-begin \\frac{4}{5} ifly-latex-end  ifly-latex-begin \\times ifly-latex-end  ifly-latex-begin \\frac{2}{7} ifly-latex-end = ifly-latex-begin \\frac{6}{55} ifly-latex-end  ifly-latex-begin \\times ifly-latex-end  ifly-latex-begin \\frac{5}{3} ifly-latex-end = ifly-latex-begin \\frac{5}{6} ifly-latex-end  ifly-latex-begin \\times ifly-latex-end  ifly-latex-begin \\frac{22}{25} ifly-latex-end =");
        for (String s : list) {
            testHtml(s,"");
        }
    }

    @Test
    public void paramsTest() throws Exception {
        List<AnchorPredictParams> initparms = initparms("sig");
        for (AnchorPredictParams anchorPredictParams : initparms) {
            testParams(anchorPredictParams);
        }
    }

    public static void testHtml(String content, String type) throws IOException {
        System.out.println("---------------------------------------------");
        System.out.println("Original:" + content);
//        System.out.println("Html:" + ParamAnalysisHolder.of("02_03","",content));
        System.out.println("Ocr:" + ParamAnalysisHolder.of("02_03", Constant.TOPIC_OCR_TYPE,content));
        System.out.println("---------------------------------------------\n");
//        Map<String, String> m = new HashMap<>();
//        m.put("test", content);
//        System.out.println(AnchorPredictEngineServiceImpl.topicBodyToModelParamstest(m, "02_03", type));
        System.out.println("-");
        System.out.println("-");
        System.out.println("-");
    }

    public static void testParams(AnchorPredictParams initparms) throws IOException {
//        System.out.println("入参："+ JSON.toJSONString(initparms));
        AnchorPredictEngineServiceImpl.topicBodyToModelParams(initparms, "02_03",new HashMap<>());
//        System.out.println(AnchorPredictEngineServiceImpl.topicBodyToModelParams(initparms, "02_03"));
        System.out.println("-");
        System.out.println("-");
        System.out.println("-");
    }
}
