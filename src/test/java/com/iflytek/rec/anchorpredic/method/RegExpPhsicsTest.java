package com.iflytek.rec.anchorpredic.method;

import com.iflytek.rec.anchorpredic.impl.AnchorPredicMethodImpl;
import com.iflytek.rec.anchorpredic.impl.AnchorPredictEngineServiceImpl;
import com.iflytek.rec.anchorpredic.interfaces.IAnchorPredictMethod;
import com.iflytek.rec.anchorpredic.method.paramAnalysis.ParamAnalysisHolder;
import com.iflytek.rec.anchorpredic.pojo.AnchorPredictParams;
import com.iflytek.rec.anchorpredic.pojo.BookAnchorMap;
import com.iflytek.rec.anchorpredic.pojo.UserInfo;
import com.iflytek.rec.anchorpredic.utils.Constant;
import com.iflytek.rec.anchorpredic.datafactory.holder.FileReaderUtil;
import org.apache.commons.lang.StringEscapeUtils;
import org.junit.Test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.iflytek.rec.anchorpredic.datafactory.holder.FileReaderUtil.supportAnchors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/12/8 13:56
 */
public class RegExpPhsicsTest {
    static IAnchorPredictMethod iAnchorPredictMethod = new AnchorPredicMethodImpl();

    private static List<AnchorPredictParams> initparms() throws Exception {
        List<AnchorPredictParams> anchorPredictParams;
        anchorPredictParams = FileReaderUtil.consistenceJuniorPhsicsTestAll();
        String resurcePattern = "02_04";
        if (!anchorPredictParams.isEmpty()) {
            UserInfo userInfo = anchorPredictParams.get(0).getUserInfo();
            resurcePattern = userInfo.getSubjectCode() + "_" + userInfo.getPhaseCode();
        }

        List<BookAnchorMap> anchorMaps = supportAnchors(resurcePattern);
        Map<String, List<String>> bookanchormap = new HashMap<>();
        for (BookAnchorMap bookAnchorMap : anchorMaps) {
            bookanchormap.put(bookAnchorMap.getBookV(), bookAnchorMap.getSupporanhcors());
        }
        for (AnchorPredictParams anchorPredictParams1 : anchorPredictParams) {
            anchorPredictParams1.setSupportAnchors(bookanchormap.get(anchorPredictParams1.getUserInfo().getBookCode()));
        }
        return anchorPredictParams;
    }

    public static void testHtml(String content) throws IOException {
        System.out.println("---------------------------------------------");
        System.out.println("Original:" + content);
        System.out.println("Html:" + ParamAnalysisHolder.of("05_04","",content));
        System.out.println("---------------------------------------------\n");
        System.out.println("-");
        System.out.println("-");
        System.out.println("-");
    }

    public static void testOcr(String content) throws IOException {
        System.out.println("---------------------------------------------");
        System.out.println("Original:" + content);
        System.out.println("Ocr:" + ParamAnalysisHolder.of("05_04", Constant.TOPIC_OCR_TYPE, content));
        System.out.println("---------------------------------------------\n");
        System.out.println("-");
        System.out.println("-");
        System.out.println("-");
    }

    public static void testPhsicsParams(AnchorPredictParams initparms) throws IOException {
//        System.out.println("入参："+ JSON.toJSONString(initparms));
        Map<String,String> map = new HashMap<>();
        AnchorPredictEngineServiceImpl.topicBodyToModelParams(initparms, "05_04",map);
//        System.out.println(AnchorPredictEngineServiceImpl.topicBodyToModelParams(initparms, "02_03"));
        System.out.println("-");
        System.out.println("-");
        System.out.println("-");
    }

    //ocr入参 参数解析  调试  一致性测试
    @Test
    public void OcrParmsTest() throws IOException {


        List<String> list = new ArrayList<>();
        String s1 = "9.简答题:拍电影时,电\n影道\t其中的\"大石头\"山泡沫塑料制成,这样,即使抛出的\"大石头\"丽中演员,也不会造成伤害,这其中有什么道理?";
        s1.replaceAll("\\\\t","\\\\\\\\t");
        System.out.println(s1);
        list.add(StringEscapeUtils.escapeJava("9.简答题:拍电影时,电影道其中的\"大石头\"山泡沫塑料制成,这样,即使抛出的\"大石头\"丽中演员,也不会造成伤害,这其中有什么道理?"));
        for (String s : list) {
            System.out.println(s);
            testOcr(s);
        }
    }

    //html入参 参数解析  调试  一致性测试
    @Test
    public void HtmlParmsTest() throws IOException {

        List<String> list = new ArrayList<>();
//        list.add("kdd<supsd>3</sup>dkk&sup3");
//        list.add("<div><p x=\"10\" y=\"4\" width=\"1215\" height=\"45\" class=\"cut-check\" data-value=\"0\"style=\"\">学完浮力知识后,小芳同学进行了相关的实践活动.她选取质量为206g,体积为ifly-latex-begin 421cm^{3} ifly-latex-end的长方体木</p><p x=\"45\" y=\"46\" width=\"1180\" height=\"50\" class=\"cut-check\" data-value=\"1\" style=\"\">块,让它漂在水面上,则它受到的浮力为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>N.若将该木块轻放在盐水中,则它浸在盐水中的体积</p><p x=\"45\" y=\"91\" width=\"659\" height=\"47\" class=\"cut-check\" data-value=\"2\" style=\"\">为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>ifly-latex-begin cm^{3} ifly-latex-end.(ifly-latex-begin \\rho ifly-latex-endifly-latex-begin _{盐水} ifly-latex-end=1.03ifly-latex-begin \\times ifly-latex-endifly-latex-begin 10^{3} ifly-latex-endkg/ifly-latex-begin m^{3} ifly-latex-end,g取10N/kg)</p></div>");
        list.add("<div><p>(1)3.333<img src=\"https://bj.dow/dladjlajd.png\" data-latex=\"%5Ccdots\" w=\"22px\" h=\"11px\" />=3.3</p><p>( )</p></div>");
        for (String s : list) {
            testHtml(s);
        }
    }


    //接口入参  参数解析 调试  一致性测试
    @Test
    public void paramsPhsicsTest() throws Exception {
        List<AnchorPredictParams> initparms = initparms();
        for (AnchorPredictParams anchorPredictParams : initparms) {
            testPhsicsParams(anchorPredictParams);
        }
    }
}
