package com.iflytek.rec.anchorpredic.method;

import org.junit.Test;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ygyang5
 * @create : 2022/10/18 15:52
 */
public class LatexAnalysChTest {
    private static String INPUT = "一十五 \\frac{12}{haha1 } \\left( \\textarea{999888777}";
    private static String REGEX1 = "(\\\\frac\\s*\\{(.+?)\\}\\s*\\{(.+?)\\})";
    private static String REGEX2 = "\\\\[a-z]+\\s*\\{(.*?)\\}";
    private static String REGEX3 = "\\\\left|\\\\right|\\\\rm";
    private static String REGEX4 = "\\{(.*?)\\}";
    private static String REGEX5 = "\\s\\s+";
    private static String REGEX6 = "\\\\";
    private static String REGEX7 = "一十(.)万";
    private static String REGEX8 = "一十(.)";
    private static String blank = "";
    private static String blank2 = "  ";

    //    private static String REPLACE2 = "\\1/";
    @Test
    public  void test() {
        Pattern p1 = Pattern.compile(REGEX1);
        Pattern p2 = Pattern.compile(REGEX2);
        Pattern p3 = Pattern.compile(REGEX3);
        Pattern p4 = Pattern.compile(REGEX4);
        Pattern p5 = Pattern.compile(REGEX5);
        Pattern p6 = Pattern.compile(REGEX6);
        Pattern p7 = Pattern.compile(REGEX7);
        Pattern p8 = Pattern.compile(REGEX8);
        // get a matcher object
        Matcher m1 = p1.matcher(INPUT);
        if (m1.find()) {

            INPUT = m1.replaceAll(m1.group(2) + "/" + m1.group(3));

        }

        Matcher m2 = p2.matcher(INPUT);
        if (m2.find()) {
            INPUT = m2.replaceAll(m2.group(1));
        }

        Matcher m3 = p3.matcher(INPUT);
        if (m3.find()) {
            INPUT = m3.replaceAll(blank);
        }

        Matcher m4 = p4.matcher(INPUT);
        while (m4.find()) {
            INPUT = m4.replaceFirst(m4.group(1));
            m4 = p4.matcher(INPUT);
        }

        Matcher m5 = p5.matcher(INPUT);
        if (m5.find()) {
            INPUT = m5.replaceAll(blank2);
        }

        Matcher m6 = p6.matcher(INPUT);
        if (m6.find()) {
            INPUT = m6.replaceAll(blank);
        }

        Matcher m7 = p7.matcher(INPUT);
        if (m7.find()) {
            INPUT = m7.replaceAll("十" + m7.group(1) + "万");
        }

        Matcher m8 = p8.matcher(INPUT);
        if (m8.find()) {
            INPUT = m8.replaceAll("十" + m8.group(1));
        }
        System.out.println(INPUT);

    }
}
