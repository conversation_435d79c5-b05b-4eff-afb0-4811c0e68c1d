package com.iflytek.rec.anchorpredic.method;

import com.iflytek.rec.anchorpredic.exception.EngineServiceException;
import com.iflytek.rec.anchorpredic.impl.lhsvote.PhotoPortraitServiceImpl;
import com.iflytek.rec.anchorpredic.pojo.lhsvote.*;
import com.iflytek.rec.anchorpredic.utils.lhsvote.FileReaderUtilLhs;
import org.junit.Test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class LhsVoteTest {
    PhotoPortraitServiceImpl photoPortraitService = new PhotoPortraitServiceImpl();
    @Test
    public void test1() throws EngineServiceException, IOException {
        FileReaderUtilLhs.DataApiConfigReader("C:\\Users\\<USER>\\Desktop\\anchorpredict\\aianchorpredict\\src\\main\\resources\\application.properties");
        PointRecgRequest pointRecgRequest = new PointRecgRequest();
        pointRecgRequest.setPhotoId("111");
        pointRecgRequest.setUserId("111");
        pointRecgRequest.setBizCode("zsy_xxj");
        pointRecgRequest.setSubjectCode("05");
        pointRecgRequest.setPhaseCode("04");
        pointRecgRequest.setGradeCode("07");
        pointRecgRequest.setBookVersionCode("01");
        pointRecgRequest.setBookCode("010001");
        pointRecgRequest.setPointType("anchorPoint");
        List<PhotoTopicInput> list = new ArrayList<>();
        pointRecgRequest.setPhotoTopicInputList(list);
        PhotoTopicInput photoTopicInput = new PhotoTopicInput();
        list.add(photoTopicInput);
        List<TopicInfo> list1 = new ArrayList<>();
        TopicInfo topicInfo = new TopicInfo();
        list1.add(topicInfo);
        topicInfo.setCfdsLevel(1d);
        topicInfo.setTopicId("122topicid");
        photoTopicInput.setTopicInfos(list1);
        photoTopicInput.setId("122");
        photoTopicInput.setScore(1d);
        photoTopicInput.setStandardScore(1d);
        pointRecgRequest.setScene(new Scene());


        PointRecgResponse response = photoPortraitService.pointRecg(pointRecgRequest);
    }
}
